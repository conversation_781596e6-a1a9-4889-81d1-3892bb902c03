runtime: python39

# Default service configuration
# This serves as the main entry point and can redirect to appropriate services

# Instance configuration
instance_class: F1
automatic_scaling:
  min_instances: 1
  max_instances: 3

# Static file handlers for documentation and main landing
handlers:
  # Handle documentation
  - url: /docs
    static_dir: docs
    secure: always

  # Redirect API requests to the API service
  - url: /api/.*
    script: auto
    secure: always

  # Redirect admin requests to the admin service
  - url: /admin/.*
    script: auto
    secure: always

  # Redirect zoom requests to the zoom service
  - url: /zoom/.*
    script: auto
    secure: always

  # Handle main application (user service)
  - url: /.*
    script: auto
    secure: always

# Environment variables
env_variables:
  ENVIRONMENT: production
