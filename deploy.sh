#!/bin/bash

# Pinlearn Google App Engine Deployment Script
# This script builds and deploys all services to Google App Engine

set -e  # Exit on any error

echo "🚀 Starting Pinlearn deployment to Google App Engine..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Error: gcloud CLI is not installed. Please install it first."
    echo "Visit: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Error: Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Function to build Angular apps
build_angular_app() {
    local app_dir=$1
    local app_name=$2
    
    echo "📦 Building $app_name..."
    cd "$app_dir"
    
    if [ ! -d "node_modules" ]; then
        echo "📥 Installing dependencies for $app_name..."
        npm install
    fi
    
    echo "🔨 Building $app_name for production..."
    npm run build --prod
    
    cd - > /dev/null
}

# Function to deploy a service
deploy_service() {
    local service_dir=$1
    local service_name=$2
    
    echo "🚀 Deploying $service_name service..."
    cd "$service_dir"
    gcloud app deploy app.yaml --quiet
    cd - > /dev/null
}

# Build all Angular applications
echo "📦 Building frontend applications..."

build_angular_app "v4/user" "User Frontend"
build_angular_app "v4/admin" "Admin Frontend" 
build_angular_app "v4/zoom" "Zoom Integration"

# Build SSR application
echo "📦 Building SSR application..."
cd "v4/ssr"
if [ ! -d "node_modules" ]; then
    echo "📥 Installing dependencies for SSR..."
    npm install
fi
echo "🔨 Building SSR for production..."
npm run build:ssr
cd - > /dev/null

# Prepare API service
echo "📦 Preparing API service..."
cd "v4/api"
if [ ! -d "node_modules" ]; then
    echo "📥 Installing dependencies for API..."
    npm install --production
fi
cd - > /dev/null

# Deploy services in order
echo "🚀 Starting deployment of all services..."

# Deploy API service first (backend)
deploy_service "v4/api" "API"

# Deploy SSR service
deploy_service "v4/ssr" "SSR"

# Deploy frontend services
deploy_service "v4/user" "User Frontend"
deploy_service "v4/admin" "Admin Frontend"
deploy_service "v4/zoom" "Zoom Integration"

# Deploy default service last
deploy_service "." "Default"

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Your services are now available at:"
echo "   • Default: https://YOUR_PROJECT_ID.appspot.com"
echo "   • API: https://api-dot-YOUR_PROJECT_ID.appspot.com"
echo "   • User: https://user-dot-YOUR_PROJECT_ID.appspot.com"
echo "   • Admin: https://admin-dot-YOUR_PROJECT_ID.appspot.com"
echo "   • SSR: https://ssr-dot-YOUR_PROJECT_ID.appspot.com"
echo "   • Zoom: https://zoom-dot-YOUR_PROJECT_ID.appspot.com"
echo ""
echo "🔧 Don't forget to:"
echo "   1. Update environment variables in each app.yaml file"
echo "   2. Configure your database connections"
echo "   3. Set up your domain mapping if needed"
echo "   4. Configure SSL certificates"
echo ""
echo "📖 For more information, visit: https://cloud.google.com/appengine/docs"
