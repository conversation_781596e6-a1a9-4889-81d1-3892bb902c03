# API Service Troubleshooting Guide

## Current Status
- **SSR**: ✅ Loading (requires JavaScript)
- **API**: ❌ Service Unavailable 
- **Admin**: ✅ Loading successfully

## Issues Found and Fixed

### 1. Missing Production Config File
**Problem**: API was looking for `production.json` config file that didn't exist.
**Solution**: ✅ Created `v4/api/server/config/production.json`

### 2. Missing Health Check Endpoint
**Problem**: App.yaml expects `/health` endpoint but it wasn't defined.
**Solution**: ✅ Added health check route in `v4/api/server/module/system/routes/health.route.js`

### 3. Node.js Version Mismatch
**Problem**: package.json specified Node 16.20.2 but app.yaml uses nodejs22.
**Solution**: ✅ Updated package.json to use Node 22.x

### 4. Missing Critical Environment Variables
**Problem**: API requires MongoDB and Redis connections to start.
**Solution**: ✅ Added required environment variables to app.yaml

## Critical Environment Variables That Need Configuration

### Database Connections (REQUIRED)
```yaml
MONGO_URI: mongodb://your-mongodb-connection-string/livelearn
REDIS_HOST: your-redis-host
REDIS_PORT: 6379
REDIS_PASS: your-redis-password
REDIS_URL: redis://your-redis-host:6379/0
```

### Email Service (REQUIRED)
```yaml
SENDGRID_API_KEY: your-sendgrid-api-key
MAIL_SERVICE: sendgrid
mailFrom: <EMAIL>
```

## Next Steps to Fix API Service

### 1. Set Up Database Services
You need to configure external database services:

**MongoDB Options:**
- MongoDB Atlas (recommended for production)
- Google Cloud Firestore in Datastore mode
- Self-hosted MongoDB instance

**Redis Options:**
- Google Cloud Memorystore for Redis
- Redis Labs Cloud
- Self-hosted Redis instance

### 2. Update Environment Variables
Edit `v4/api/app.yaml` and replace placeholder values:
```yaml
# Replace these with actual values:
MONGO_URI: mongodb://your-actual-mongodb-uri/livelearn
REDIS_HOST: your-actual-redis-host
REDIS_PASS: your-actual-redis-password
SENDGRID_API_KEY: your-actual-sendgrid-key
SESSION_SECRET: your-actual-session-secret
```

### 3. Deploy API Service
```bash
cd v4/api
gcloud app deploy
```

## Testing API Connectivity

Once deployed with proper database connections, test these endpoints:

1. **Health Check**: `https://api-dot-vocabulous-466003.et.r.appspot.com/health`
2. **Public Config**: `https://api-dot-vocabulous-466003.et.r.appspot.com/v1/system/configs/public`
3. **API Info**: `https://api-dot-vocabulous-466003.et.r.appspot.com/api-author`

## Common API Startup Issues

### MongoDB Connection Errors
- Verify MongoDB URI format
- Check network access/firewall rules
- Ensure database exists

### Redis Connection Errors  
- Verify Redis host and port
- Check authentication credentials
- Test Redis connectivity

### Port Binding Issues
- API should bind to port 8080 (configured in app.yaml)
- Check PORT environment variable

## Monitoring and Logs

View API logs in Google Cloud Console:
```bash
gcloud app logs tail -s api
```

Check specific error patterns:
- MongoDB connection errors
- Redis connection errors
- Missing environment variables
- Port binding failures

## Service Dependencies

The API service requires these external services to be operational:
1. **MongoDB** - Primary database
2. **Redis** - Caching and session storage  
3. **SendGrid** - Email delivery
4. **Stripe** - Payment processing (optional for basic functionality)

Without MongoDB and Redis, the API will fail to start completely.
