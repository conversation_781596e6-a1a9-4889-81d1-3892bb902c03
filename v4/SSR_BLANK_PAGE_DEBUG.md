# 🔍 SSR Blank Page Debugging Guide

## Current Status
- ✅ **API Health**: Working (`https://api-dot-vocabulous-466003.et.r.appspot.com/health`)
- ❌ **SSR Frontend**: Showing blank page after deployment

## Most Likely Causes

### 1. JavaScript Errors in Browser
The Angular app might be failing to load due to JavaScript errors.

### 2. API Connection Issues
The SSR app might be trying to connect to API during initialization and failing.

### 3. Environment Configuration Issues
Missing or incorrect environment variables.

### 4. Build Issues
The Angular build might have issues that only show up in production.

## 🔧 Debugging Steps

### Step 1: Check SSR Logs
```bash
gcloud app logs tail -s ssr --limit=50
```

**Look for:**
- Server startup messages
- JavaScript errors
- API connection errors
- Environment variable issues

### Step 2: Test SSR Health Endpoint
```bash
curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/health
```

**Expected Response:**
```json
{"status":"ok","timestamp":"2024-..."}
```

### Step 3: Check Browser Console
1. Open https://ssr-dot-vocabulous-466003.et.r.appspot.com in browser
2. Open Developer Tools (F12)
3. Check Console tab for JavaScript errors
4. Check Network tab for failed requests

**Common Errors to Look For:**
- `Failed to load resource` (API calls failing)
- `ChunkLoadError` (Angular chunks not loading)
- `TypeError` (Missing dependencies)
- CORS errors

### Step 4: Test Static Files
```bash
# Test if CSS is loading
curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/styles.7a344625451ef300.css

# Test if main JS is loading  
curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/main.f477bf4df900e67e.js
```

### Step 5: Check Index.html Content
```bash
curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/
```

**Should contain:**
- `<app-root>` tag
- Script tags for Angular bundles
- CSS links

## 🚨 Quick Fixes to Try

### Fix 1: Rebuild and Redeploy SSR
```bash
cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

# Clean build
rm -rf dist/
npm run build

# Deploy
gcloud app deploy
```

### Fix 2: Check Environment Variables
Verify these are set in SSR app.yaml:
```yaml
env_variables:
  NODE_ENV: production
  API_BASE_URL: https://api-dot-vocabulous-466003.et.r.appspot.com/v1
  SOCKET_URL: https://api-dot-vocabulous-466003.et.r.appspot.com
```

### Fix 3: Test with Simple HTML
Create a test endpoint to verify server is working:

Add to `server-production.js`:
```javascript
server.get('/test', (req, res) => {
  res.send('<h1>SSR Server is Working!</h1><p>Time: ' + new Date() + '</p>');
});
```

### Fix 4: Disable API Calls Temporarily
If the issue is API connection during app initialization, temporarily disable API calls in:
- `src/app/app.component.ts`
- `src/environments/environment.prod.ts`

## 🎯 Expected Behavior

### ✅ Working SSR Should Show:
- Proper HTML content with Angular components
- Loading indicators while fetching data
- Functional navigation and UI elements

### ❌ Blank Page Indicates:
- JavaScript errors preventing app initialization
- Failed API calls blocking app startup
- Missing or corrupted build files
- Environment configuration issues

## 📋 Diagnostic Checklist

Run these commands and share the results:

1. **SSR Logs:**
   ```bash
   gcloud app logs tail -s ssr --limit=20
   ```

2. **SSR Health:**
   ```bash
   curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/health
   ```

3. **Index.html Content:**
   ```bash
   curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/ | head -50
   ```

4. **Browser Console Errors:**
   - Open browser dev tools
   - Visit the SSR URL
   - Share any red errors in console

## 🔄 Temporary Workaround

If you need the SSR working immediately, you can:

1. **Use Client-Side Rendering Only:**
   - Modify server to serve static files only
   - Disable SSR temporarily
   - Focus on getting the SPA working first

2. **Test Locally:**
   ```bash
   cd /Users/<USER>/Downloads/Pinlearn/v4/ssr
   npm run build
   npm start
   # Visit http://localhost:8080
   ```

## 📞 Next Steps

1. **Share the SSR logs** - this will show the exact issue
2. **Check browser console** - JavaScript errors will be visible
3. **Test the health endpoint** - verify server is responding
4. **Try the rebuild** - clean build might fix corruption issues

The logs will tell us exactly what's preventing the Angular app from loading!
