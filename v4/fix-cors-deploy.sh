#!/bin/bash

echo "🔧 Fixing CORS Issue and Deploying API"
echo "======================================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/api

echo "📋 CORS Issue Summary:"
echo "- SSR frontend: https://ssr-dot-vocabulous-466003.et.r.appspot.com"
echo "- API backend: https://api-dot-vocabulous-466003.et.r.appspot.com"
echo "- Problem: CORS policy blocking cross-origin requests"
echo ""

echo "✅ CORS Fixes Applied:"
echo "- Added adminURL environment variable"
echo "- Enhanced CORS whitelist to include App Engine domains"
echo "- Added fallback for same-project requests"
echo "- Added CORS debugging endpoint"
echo ""

echo "🚀 Deploying API with CORS fixes..."
gcloud app deploy --quiet

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 API deployment successful!"
    echo ""
    
    echo "🔍 Testing CORS configuration:"
    echo ""
    
    echo "1. API Health Check:"
    curl -s https://api-dot-vocabulous-466003.et.r.appspot.com/health
    echo ""
    echo ""
    
    echo "2. CORS Test Endpoint:"
    curl -s -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         https://api-dot-vocabulous-466003.et.r.appspot.com/cors-test
    echo ""
    echo ""
    
    echo "3. Categories API (the one that was failing):"
    curl -s -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/categories?take=99&isActive=true&sort=ordering&sortType=asc"
    echo ""
    echo ""
    
    echo "🌐 Now test your SSR frontend:"
    echo "https://ssr-dot-vocabulous-466003.et.r.appspot.com"
    echo ""
    echo "Expected results:"
    echo "- ✅ No CORS errors in browser console"
    echo "- ✅ API calls succeed (200 status)"
    echo "- ✅ Content loads instead of blank page"
    echo ""
    
    echo "📊 Monitor API logs for CORS debugging:"
    echo "gcloud app logs tail -s api --limit=20"
    
else
    echo ""
    echo "❌ API deployment failed!"
    echo "Check the error messages above."
fi
