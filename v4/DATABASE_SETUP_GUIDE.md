# Database Setup Guide - Fix SSR Loading Issue

## 🎯 Goal
Set up MongoDB Atlas and Redis Labs to get your API service running, which will fix the SSR frontend loading issue.

## 📋 Prerequisites
- MongoDB Atlas account
- Redis Labs account  
- SendGrid account (optional but recommended)

## Step 1: MongoDB Atlas Setup

### 1.1 Create MongoDB Atlas Account
- Visit: https://www.mongodb.com/cloud/atlas/register
- Sign up for free account

### 1.2 Create Cluster
1. Click "Create" → "Shared" (Free tier)
2. Choose **Google Cloud Platform** (same as your App Engine)
3. Select region closest to your App Engine region
4. Cluster name: `vocabulous-cluster` (or any name)
5. Click "Create Cluster"

### 1.3 Create Database User
1. Go to "Database Access" → "Add New Database User"
2. Authentication Method: Password
3. Username: `vocabulous-user`
4. Password: Generate secure password (save it!)
5. Database User Privileges: "Read and write to any database"
6. Click "Add User"

### 1.4 Configure Network Access
1. Go to "Network Access" → "Add IP Address"
2. Click "Allow Access from Anywhere" (0.0.0.0/0)
3. Comment: "App Engine Access"
4. Click "Confirm"

### 1.5 Get Connection String
1. Go to "Clusters" → Click "Connect" on your cluster
2. Choose "Connect your application"
3. Driver: Node.js, Version: 4.1 or later
4. Copy the connection string (looks like):
   ```
   mongodb+srv://vocabulous-user:<password>@vocabulous-cluster.xxxxx.mongodb.net/livelearn
   ```
5. Replace `<password>` with your actual password
6. **Save this connection string!**

## Step 2: Redis Labs Setup

### 2.1 Create Redis Labs Account
- Visit: https://redis.com/try-free/
- Sign up for free account

### 2.2 Create Database
1. Click "New Database"
2. Plan: "Fixed" (Free tier)
3. Cloud: **Google Cloud Platform**
4. Region: Same as your App Engine region
5. Database name: `vocabulous-redis`
6. Click "Create Database"

### 2.3 Get Connection Details
1. Click on your database name
2. Note down:
   - **Host**: `redis-xxxxx.c1.us-central1-1.gce.cloud.redislabs.com`
   - **Port**: `xxxxx`
   - **Password**: Click "eye" icon to reveal
3. **Save these details!**

## Step 3: SendGrid Setup (Optional)

### 3.1 Create SendGrid Account
- Visit: https://signup.sendgrid.com/
- Sign up for free account

### 3.2 Create API Key
1. Go to Settings → API Keys
2. Click "Create API Key"
3. Name: "Vocabulous API"
4. Permissions: "Full Access"
5. Click "Create & View"
6. **Copy and save the API key** (starts with `SG.`)

## Step 4: Update Configuration

### 4.1 Run Setup Script
Once you have all the credentials, run:

```bash
cd /Users/<USER>/Downloads/Pinlearn/v4

# Replace with your actual values:
./setup-databases.sh \
  'mongodb+srv://vocabulous-user:<EMAIL>/livelearn' \
  'redis-xxxxx.c1.us-central1-1.gce.cloud.redislabs.com' \
  'YOUR_REDIS_PORT' \
  'YOUR_REDIS_PASSWORD' \
  'SG.YOUR_SENDGRID_KEY'
```

### 4.2 Manual Update (Alternative)
If the script doesn't work, manually edit `v4/api/app.yaml`:

```yaml
env_variables:
  # Replace these placeholders:
  MONGO_URI: mongodb+srv://vocabulous-user:<EMAIL>/livelearn
  REDIS_HOST: redis-xxxxx.c1.us-central1-1.gce.cloud.redislabs.com
  REDIS_PORT: YOUR_REDIS_PORT
  REDIS_PASS: YOUR_REDIS_PASSWORD
  REDIS_URL: redis://default:<EMAIL>:YOUR_REDIS_PORT
  SENDGRID_API_KEY: SG.YOUR_SENDGRID_KEY
  SESSION_SECRET: GENERATE_RANDOM_32_CHAR_STRING
```

## Step 5: Deploy Services

### 5.1 Deploy API Service
```bash
cd /Users/<USER>/Downloads/Pinlearn/v4/api
gcloud app deploy
```

### 5.2 Test API Health
```bash
curl https://api-dot-vocabulous-466003.et.r.appspot.com/health
```
Should return: `{"status":"OK","timestamp":"...","service":"API","version":"4.0.0"}`

### 5.3 Deploy SSR Service
```bash
cd /Users/<USER>/Downloads/Pinlearn/v4/ssr
gcloud app deploy
```

### 5.4 Test SSR Frontend
Visit: https://ssr-dot-vocabulous-466003.et.r.appspot.com
Should show proper content instead of "Please enable JavaScript"

## 🔍 Troubleshooting

### API Still Not Working?
1. Check logs: `gcloud app logs tail -s api`
2. Verify MongoDB connection string format
3. Ensure Redis credentials are correct
4. Check network access settings

### SSR Still Showing "Please enable JavaScript"?
1. Ensure API health check passes first
2. Check SSR logs: `gcloud app logs tail -s ssr`
3. Verify API_BASE_URL in SSR environment

## 📞 Need Help?
If you encounter issues:
1. Share the specific error messages
2. Check the service logs
3. Verify all credentials are correct
4. Ensure database services are running

Once completed, your services should communicate properly and the SSR frontend will load correctly!
