#!/bin/bash

echo "🔧 Final CORS Fix Deployment"
echo "============================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/api

echo "📋 CORS Configuration Summary:"
echo "- Enhanced CORS options with proper preflight handling"
echo "- Explicit OPTIONS method support"
echo "- App Engine domain whitelist"
echo "- Proper headers and methods configuration"
echo ""

echo "🚀 Deploying API with comprehensive CORS fix..."
gcloud app deploy --quiet

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 API deployment successful!"
    echo ""
    
    echo "🔍 Testing CORS configuration step by step:"
    echo ""
    
    echo "1. Basic API Health (no CORS):"
    curl -s https://api-dot-vocabulous-466003.et.r.appspot.com/health | jq .
    echo ""
    
    echo "2. CORS Test Endpoint:"
    curl -s -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         https://api-dot-vocabulous-466003.et.r.appspot.com/cors-test | jq .
    echo ""
    
    echo "3. OPTIONS Preflight Test (Categories):"
    curl -s -X OPTIONS \
         -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         -H "Access-Control-Request-Method: GET" \
         -H "Access-Control-Request-Headers: Content-Type" \
         -v "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/categories" 2>&1 | grep -E "(Access-Control|HTTP/)"
    echo ""
    
    echo "4. Actual Categories API Call:"
    curl -s -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/categories?take=5&isActive=true" | jq '.data.items[0:2]'
    echo ""
    
    echo "5. System Config API Call:"
    curl -s -H "Origin: https://ssr-dot-vocabulous-466003.et.r.appspot.com" \
         "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/system/configs/public" | jq '.data | keys'
    echo ""
    
    echo "🌐 Now test your SSR frontend:"
    echo "https://ssr-dot-vocabulous-466003.et.r.appspot.com"
    echo ""
    echo "Expected results:"
    echo "- ✅ No CORS errors in browser console"
    echo "- ✅ Categories load successfully"
    echo "- ✅ System config loads successfully"
    echo "- ✅ Content displays instead of blank page"
    echo ""
    
    echo "📊 If still having issues, check API logs:"
    echo "gcloud app logs tail -s api --limit=20"
    
else
    echo ""
    echo "❌ API deployment failed!"
    echo "Check the error messages above."
fi
