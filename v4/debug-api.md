# 🔍 API Service Debugging Guide

## Current Issue
API health endpoint returns "Service Unavailable" after deployment.

## Most Likely Causes

### 1. MongoDB Connection Failure
The API tries to connect to MongoDB on startup. If this fails, the entire service fails.

**Your MongoDB URI:** `mongodb+srv://ollieshing:<EMAIL>/livelearn`

**Potential Issues:**
- MongoDB Atlas cluster is paused/stopped
- Network access not configured for App Engine
- Database name doesn't exist
- Credentials are incorrect

### 2. Redis Connection Failure
The API also connects to Redis for sessions and caching.

**Your Redis Config:**
- Host: `redis-15686.c334.asia-southeast2-1.gce.redns.redis-cloud.com`
- Port: `15686`
- Password: `qssl4qzknezoR2Z1WMTBPvTjYR2RsjIe`

## 🔧 Debugging Steps

### Step 1: Check API Logs
```bash
gcloud app logs tail -s api --limit=50
```

**Look for these error patterns:**
- `MongooseError` or `MongoDB connection failed`
- `Redis connection failed` or `ECONNREFUSED`
- `Error: listen EADDRINUSE` (port already in use)
- `Cannot read property` or `TypeError` (missing environment variables)

### Step 2: Check Service Status
```bash
gcloud app services list
gcloud app versions list --service=api
gcloud app instances list --service=api
```

### Step 3: Test MongoDB Connection
**Verify your MongoDB Atlas cluster:**
1. Go to https://cloud.mongodb.com/
2. Check if your cluster is running (not paused)
3. Verify network access allows `0.0.0.0/0`
4. Test connection string in MongoDB Compass or CLI

### Step 4: Test Redis Connection
**Verify your Redis Labs instance:**
1. Go to https://app.redislabs.com/
2. Check if your database is active
3. Test connection with redis-cli:
```bash
redis-cli -h redis-15686.c334.asia-southeast2-1.gce.redns.redis-cloud.com -p 15686 -a qssl4qzknezoR2Z1WMTBPvTjYR2RsjIe ping
```

## 🚨 Common Fixes

### Fix 1: MongoDB Atlas Network Access
1. Go to MongoDB Atlas → Network Access
2. Add IP Address: `0.0.0.0/0` (Allow access from anywhere)
3. Comment: "Google App Engine"

### Fix 2: MongoDB Atlas Cluster Paused
1. Go to MongoDB Atlas → Clusters
2. If cluster shows "Paused", click "Resume"
3. Wait for cluster to become active

### Fix 3: Redis Labs Database Inactive
1. Go to Redis Labs → Databases
2. Check if database status is "Active"
3. If not, contact Redis Labs support

### Fix 4: Environment Variables
Check if all required environment variables are set in app.yaml:
```yaml
env_variables:
  NODE_ENV: production
  PORT: 8080
  MONGO_URI: mongodb+srv://ollieshing:<EMAIL>/livelearn
  REDIS_HOST: redis-15686.c334.asia-southeast2-1.gce.redns.redis-cloud.com
  REDIS_PORT: 15686
  REDIS_PASS: qssl4qzknezoR2Z1WMTBPvTjYR2RsjIe
  SESSION_SECRET: VocabulousSecureSessionKey2024RandomString123456789
```

## 🎯 Expected Log Messages

### ✅ Successful Startup:
```
mongoose connection open to mongodb+srv://...
Express server listening on 8080, in production mode
agenda ready
```

### ❌ Failed Startup:
```
MongooseError: Could not connect to any servers in your MongoDB Atlas cluster
Error: connect ECONNREFUSED
Error: Authentication failed
```

## 🔄 Quick Fix Attempt

If MongoDB Atlas is the issue, try this temporary fix:

1. **Create a simpler MongoDB URI** (without special characters):
   - Go to MongoDB Atlas
   - Create a new database user with a simple password (no special chars)
   - Get a new connection string
   - Update app.yaml and redeploy

2. **Test with local MongoDB** (temporary):
   ```yaml
   MONGO_URI: mongodb://localhost:27017/livelearn
   ```
   (This won't work in production but helps isolate the issue)

## 📞 Next Steps

1. **Run the debugging commands above**
2. **Share the API logs** - this will show the exact error
3. **Verify MongoDB Atlas cluster is active**
4. **Verify Redis Labs database is active**
5. **Check network access settings**

The logs will tell us exactly what's failing during startup!
