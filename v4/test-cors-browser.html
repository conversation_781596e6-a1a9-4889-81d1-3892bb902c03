<!DOCTYPE html>
<html>
  <head>
    <title>CORS Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      .test {
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #ccc;
      }
      .success {
        background-color: #d4edda;
        border-color: #c3e6cb;
      }
      .error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
      }
      .loading {
        background-color: #fff3cd;
        border-color: #ffeaa7;
      }
      pre {
        background: #f8f9fa;
        padding: 10px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>🔍 CORS Test for Vocabulous API</h1>
    <p>Testing CORS from SSR domain to API domain</p>

    <div id="test1" class="test loading">
      <h3>Test 1: CORS Test Endpoint</h3>
      <p>Testing: <code>GET /cors-test</code></p>
      <div id="result1">Loading...</div>
    </div>

    <div id="test2" class="test loading">
      <h3>Test 2: Categories API</h3>
      <p>Testing: <code>GET /v1/categories</code></p>
      <div id="result2">Loading...</div>
    </div>

    <div id="test3" class="test loading">
      <h3>Test 3: System Config API</h3>
      <p>Testing: <code>GET /v1/system/configs/public</code></p>
      <div id="result3">Loading...</div>
    </div>

    <script>
      async function testCORS(url, testId) {
        const testDiv = document.getElementById(testId);
        const resultDiv = document.getElementById("result" + testId.slice(-1));

        try {
          console.log("Testing:", url);

          // First test OPTIONS preflight
          const preflightResponse = await fetch(url, {
            method: "OPTIONS",
            headers: {
              Origin: window.location.origin,
              "Access-Control-Request-Method": "GET",
              "Access-Control-Request-Headers": "Content-Type",
            },
          });

          console.log(
            "Preflight response:",
            preflightResponse.status,
            preflightResponse.headers
          );

          // Then test actual request
          const response = await fetch(url, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          });

          const data = await response.text();

          if (response.ok) {
            testDiv.className = "test success";
            resultDiv.innerHTML = `
                        <strong>✅ SUCCESS</strong><br>
                        Preflight: ${preflightResponse.status}<br>
                        Actual: ${response.status}<br>
                        CORS Headers: ${
                          response.headers.get("Access-Control-Allow-Origin") ||
                          "None"
                        }<br>
                        <pre>${data.substring(0, 500)}${
              data.length > 500 ? "..." : ""
            }</pre>
                    `;
          } else {
            testDiv.className = "test error";
            resultDiv.innerHTML = `
                        <strong>❌ HTTP ERROR</strong><br>
                        Preflight: ${preflightResponse.status}<br>
                        Actual: ${response.status}<br>
                        <pre>${data}</pre>
                    `;
          }
        } catch (error) {
          testDiv.className = "test error";
          resultDiv.innerHTML = `
                    <strong>❌ CORS/NETWORK ERROR</strong><br>
                    Error: ${error.message}<br>
                    <pre>${error.stack}</pre>
                `;
          console.error("CORS test failed:", error);
        }
      }

      // Run tests when page loads
      window.onload = function () {
        testCORS(
          "https://api-dot-vocabulous-466003.et.r.appspot.com/cors-test",
          "test1"
        );
        testCORS(
          "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/categories?take=99&isActive=true&sort=ordering&sortType=asc",
          "test2"
        );
        testCORS(
          "https://api-dot-vocabulous-466003.et.r.appspot.com/v1/system/configs/public",
          "test3"
        );
      };
    </script>
  </body>
</html>
