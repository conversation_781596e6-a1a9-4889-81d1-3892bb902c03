# Minimal API configuration for testing
# Copy this to app.yaml to test basic API startup without Redis/Socket features

runtime: nodejs22

# Service configuration
service: api

# Instance configuration
instance_class: F2
automatic_scaling:
  min_instances: 1
  max_instances: 8
  target_cpu_utilization: 0.6

# Environment variables - MINIMAL SETUP FOR TESTING
env_variables:
  NODE_ENV: production
  APP_NAME: LIVELEARN
  ALLOW_CORS: true
  PORT: 8080
  VERSION: regular
  
  # Service URLs
  baseUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/
  userWebUrl: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  adminWebUrl: https://admin-dot-vocabulous-466003.et.r.appspot.com
  
  # Site configuration
  SITE_NAME: LIVELEARN
  logoUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/assets/logo.png
  
  # CORS and security
  host: vocabulous-466003.et.r.appspot.com
  
  # Session configuration
  SESSION_SECRET: VocabulousSecureSessionKey2024RandomString123456789
  
  # File size limits (in MB)
  MAX_PHOTO_SIZE: 10
  MAX_VIDEO_SIZE: 200
  MAX_FILE_SIZE: 100
  
  # Social links
  facebookUrl: https://facebook.com/yourpage
  twitterUrl: https://twitter.com/yourhandle
  
  # Database connections - ONLY MONGODB FOR TESTING
  MONGO_URI: mongodb+srv://ollieshing:<EMAIL>/livelearn
  
  # DISABLE REDIS TEMPORARILY FOR TESTING
  # REDIS_HOST: redis-15686.c334.asia-southeast2-1.gce.redns.redis-cloud.com
  # REDIS_PORT: 15686
  # REDIS_DB: 0
  # REDIS_PASS: qssl4qzknezoR2Z1WMTBPvTjYR2RsjIe
  # REDIS_URL: redis://default:<EMAIL>:15686
  
  # Queue configuration
  QUEUE_PREFIX: queueCourse
  
  # Email configuration
  SENDGRID_API_KEY: SG.placeholder-key-replace-with-actual-sendgrid-key
  MAIL_SERVICE: sendgrid
  mailFrom: <EMAIL>
  ADMIN_EMAIL: <EMAIL>

# Health check configuration
readiness_check:
  path: '/health'
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: '/health'
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Network configuration
network:
  forwarded_ports:
    - 8080

# Let Express server handle all requests
handlers:
  - url: /.*
    script: auto
    secure: always

# Skip files for deployment
skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?tests$
  - ^(.*/)?test$
  - ^test/(.*/)?
  - ^COPYING.LESSER$
  - ^README\..*$
  - \.gitignore$
  - ^\.git/.*$
  - \.*\.md$
  - ^(.*/)?node_modules/(.*/)?
