#!/bin/bash

# Database Setup Script for Vocabulous API
# Run this script after setting up MongoDB Atlas and Redis Labs

echo "🚀 Setting up database connections for Vocabulous API..."

# Check if required parameters are provided
if [ $# -lt 4 ]; then
    echo "❌ Usage: $0 <MONGO_URI> <REDIS_HOST> <REDIS_PORT> <REDIS_PASSWORD> [SENDGRID_API_KEY]"
    echo ""
    echo "Example:"
    echo "$0 'mongodb+srv://user:<EMAIL>/livelearn' 'redis-12345.c1.us-central1-1.gce.cloud.redislabs.com' '12345' 'your-redis-password' 'SG.your-sendgrid-key'"
    echo ""
    echo "📋 To get these values:"
    echo "1. MongoDB Atlas: Get connection string from 'Connect' → 'Connect your application'"
    echo "2. Redis Labs: Get host, port, password from database details"
    echo "3. SendGrid: Get API key from Settings → API Keys (optional)"
    exit 1
fi

MONGO_URI="$1"
REDIS_HOST="$2"
REDIS_PORT="$3"
REDIS_PASSWORD="$4"
SENDGRID_API_KEY="${5:-your-sendgrid-api-key}"

# Construct Redis URL
REDIS_URL="redis://default:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}"

echo "📝 Updating API configuration..."

# Update app.yaml with actual values
sed -i.bak "s|REPLACE_WITH_MONGODB_ATLAS_CONNECTION_STRING|${MONGO_URI}|g" api/app.yaml
sed -i.bak "s|REPLACE_WITH_REDIS_HOST|${REDIS_HOST}|g" api/app.yaml
sed -i.bak "s|REPLACE_WITH_REDIS_PORT|${REDIS_PORT}|g" api/app.yaml
sed -i.bak "s|REPLACE_WITH_REDIS_PASSWORD|${REDIS_PASSWORD}|g" api/app.yaml
sed -i.bak "s|REPLACE_WITH_REDIS_URL|${REDIS_URL}|g" api/app.yaml
sed -i.bak "s|your-sendgrid-api-key|${SENDGRID_API_KEY}|g" api/app.yaml

# Generate a secure session secret
SESSION_SECRET=$(openssl rand -base64 32)
sed -i.bak "s|your-production-session-secret-change-this|${SESSION_SECRET}|g" api/app.yaml

echo "✅ Configuration updated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Deploy API service: cd api && gcloud app deploy"
echo "2. Test API health: curl https://api-dot-vocabulous-466003.et.r.appspot.com/health"
echo "3. Deploy SSR service: cd ssr && gcloud app deploy"
echo ""
echo "🔍 Configuration summary:"
echo "- MongoDB: ${MONGO_URI:0:50}..."
echo "- Redis: ${REDIS_HOST}:${REDIS_PORT}"
echo "- SendGrid: ${SENDGRID_API_KEY:0:20}..."
echo "- Session Secret: Generated securely"
