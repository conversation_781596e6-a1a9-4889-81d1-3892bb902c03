#!/bin/bash

echo "🔍 SSR Service Debugging Commands"
echo "=================================="
echo ""

echo "1. Check SSR service logs:"
echo "gcloud app logs tail -s ssr --limit=50"
echo ""

echo "2. Check SSR service status:"
echo "gcloud app services list"
echo ""

echo "3. Check SSR service versions:"
echo "gcloud app versions list --service=ssr"
echo ""

echo "4. Test SSR health endpoint:"
echo "curl -v https://ssr-dot-vocabulous-466003.et.r.appspot.com/health"
echo ""

echo "5. Test SSR base endpoint:"
echo "curl -v https://ssr-dot-vocabulous-466003.et.r.appspot.com/"
echo ""

echo "6. Check if SSR can connect to API:"
echo "curl -v https://ssr-dot-vocabulous-466003.et.r.appspot.com/api/test"
echo ""

echo "Run these commands to diagnose the SSR blank page issue."
