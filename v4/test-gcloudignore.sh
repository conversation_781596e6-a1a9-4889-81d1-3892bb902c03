#!/bin/bash

echo "🔍 Testing .gcloudignore Configuration"
echo "====================================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

echo "📋 Files that WILL be uploaded to App Engine:"
echo ""

# Simulate what gcloud will upload by checking what's NOT in .gcloudignore
echo "✅ Essential files:"
for file in package.json app.yaml server-production.js; do
    if [ -f "$file" ]; then
        echo "  ✓ $file"
    else
        echo "  ❌ $file (MISSING!)"
    fi
done

echo ""
echo "✅ Built application files:"
if [ -d "dist/browser" ]; then
    echo "  ✓ dist/browser/ ($(ls dist/browser/ | wc -l) files)"
    echo "  ✓ dist/browser/index.html"
    echo "  ✓ dist/browser/main.*.js"
    echo "  ✓ dist/browser/styles.*.css"
else
    echo "  ❌ dist/browser/ (MISSING! Run build first)"
fi

echo ""
echo "❌ Files that will be EXCLUDED (good):"
echo "  - src/ (TypeScript source)"
echo "  - *.ts files"
echo "  - tsconfig*.json"
echo "  - angular.json"
echo "  - server.ts"

echo ""
echo "🔍 Checking for problematic files:"
if [ -f "tsconfig.app.json" ]; then
    echo "  ⚠️  tsconfig.app.json exists but will be excluded"
else
    echo "  ❌ tsconfig.app.json missing (this was causing the error)"
fi

if [ -f "server.ts" ]; then
    echo "  ⚠️  server.ts exists but will be excluded (good)"
else
    echo "  ✓ server.ts not found"
fi

echo ""
echo "📊 Summary:"
echo "- Runtime files: ✓ Present"
echo "- Built app: $([ -d "dist/browser" ] && echo "✓ Present" || echo "❌ Missing")"
echo "- Problematic TS files: ✓ Excluded"
echo ""

if [ -d "dist/browser" ] && [ -f "package.json" ] && [ -f "server-production.js" ]; then
    echo "🎉 Ready for deployment!"
    echo "Run: ../deploy-ssr-fixed.sh"
else
    echo "❌ Not ready for deployment"
    echo "Missing required files. Run build first."
fi
