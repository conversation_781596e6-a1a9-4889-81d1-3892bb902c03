#!/bin/bash

echo "🔧 Fixing SSR Deployment Issue"
echo "==============================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

echo "📋 Issue Identified:"
echo "- SSR server build not completing properly"
echo "- Missing dist/server/main.js file"
echo "- Build script needs to run both browser and server builds"
echo ""

echo "🧹 Cleaning previous build..."
rm -rf dist/
rm -rf node_modules/.cache/
echo "✅ Cleaned"
echo ""

echo "📦 Installing dependencies..."
npm install --legacy-peer-deps
echo "✅ Dependencies installed"
echo ""

echo "🏗️ Building browser application..."
ng build --configuration=production
echo "✅ Browser build complete"
echo ""

echo "🏗️ Building SSR server..."
ng run scss-angular:server:production
echo "✅ Server build complete"
echo ""

echo "📁 Checking build output:"
echo "Browser build:"
ls -la dist/browser/ | head -5
echo ""
echo "Server build:"
ls -la dist/server/
echo ""

echo "🔍 Verifying main.js exists:"
if [ -f "dist/server/main.js" ]; then
    echo "✅ dist/server/main.js exists"
    echo "File size: $(du -h dist/server/main.js | cut -f1)"
else
    echo "❌ dist/server/main.js is missing!"
    echo "Available files in dist/server/:"
    ls -la dist/server/
    exit 1
fi
echo ""

echo "🚀 Ready for deployment!"
echo "Run this command to deploy:"
echo "gcloud app deploy"
echo ""

echo "🔍 After deployment, test:"
echo "1. Health check: curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/health"
echo "2. Main page: curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/"
echo "3. Check logs: gcloud app logs tail -s ssr --limit=20"
