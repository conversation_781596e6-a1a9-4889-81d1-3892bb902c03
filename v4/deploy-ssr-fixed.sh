#!/bin/bash

echo "🚀 SSR Deployment - Fixed Version"
echo "=================================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

echo "📋 Configuration Summary:"
echo "- Using server-production.js (stable Express server)"
echo "- Building locally (no cloud build issues)"
echo "- Uploading only built files and runtime dependencies"
echo "- Excluding all TypeScript source files"
echo ""

echo "🧹 Cleaning previous build..."
rm -rf dist/
rm -rf .angular/cache/
echo "✅ Cleaned"
echo ""

echo "📦 Installing dependencies..."
npm install --legacy-peer-deps
echo "✅ Dependencies installed"
echo ""

echo "🏗️ Building Angular application locally..."
ng build --configuration=production

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Local build complete"
echo ""

echo "📁 Checking build output:"
if [ -d "dist/browser" ]; then
    echo "✅ dist/browser/ exists"
    echo "Files: $(ls dist/browser/ | wc -l) files"
    echo "Size: $(du -sh dist/browser/ | cut -f1)"
else
    echo "❌ dist/browser/ is missing!"
    exit 1
fi
echo ""

echo "🔍 Verifying server-production.js:"
if [ -f "server-production.js" ]; then
    echo "✅ server-production.js exists"
else
    echo "❌ server-production.js is missing!"
    exit 1
fi
echo ""

echo "📋 Files to be uploaded (excluding .gcloudignore):"
echo "✅ Essential runtime files:"
ls -la | grep -E "(package\.json|app\.yaml|server-production\.js)"
echo ""
echo "✅ Built application:"
ls -la dist/browser/ | head -3
echo "... and $(ls dist/browser/ | wc -l) total files"
echo ""

echo "❌ Excluded files (won't be uploaded):"
echo "- src/ (TypeScript source)"
echo "- *.ts files (TypeScript)"
echo "- tsconfig*.json (build configs)"
echo "- angular.json (build config)"
echo ""

echo "🚀 Deploying to Google App Engine..."
echo "This will take a few minutes..."
echo ""

gcloud app deploy --quiet

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment successful!"
    echo ""
    echo "🔍 Testing deployment:"
    echo "1. Health check:"
    curl -s https://ssr-dot-vocabulous-466003.et.r.appspot.com/health
    echo ""
    echo ""
    echo "2. Main page (first 200 chars):"
    curl -s https://ssr-dot-vocabulous-466003.et.r.appspot.com/ | head -c 200
    echo ""
    echo ""
    echo "🌐 Visit your application:"
    echo "https://ssr-dot-vocabulous-466003.et.r.appspot.com"
    echo ""
    echo "📊 Monitor logs:"
    echo "gcloud app logs tail -s ssr"
else
    echo ""
    echo "❌ Deployment failed!"
    echo "Check the error messages above."
    echo ""
    echo "🔍 Common issues:"
    echo "- Build errors: Check ng build output"
    echo "- File missing: Verify dist/ folder exists"
    echo "- Config errors: Check app.yaml syntax"
fi
