import { Injectable } from '@angular/core';
import { Restangular } from 'ngx-restangular';
import { Subject, Observable } from 'rxjs';
import { Model, ModelFactory } from '../../shared/services/simple-state.service';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readNotification = new Subject<any>();
  public readNotification$ = this.readNotification.asObservable();

  public model: Model<State>;
  notification$: Observable<State>;

  constructor(private restangular: Restangular, private modelFactory: ModelFactory<State>) {
    this.model = this.modelFactory.create({
      totalNotification: 0
    });
    this.notification$ = this.model.data$;
  }

  updateTotalNotification(value: number) {
    // retrieve raw model data
    const modelSnapshot = this.model.get();

    // mutate model data
    const newModel = { ...modelSnapshot, totalNotification: value };
    console.log(newModel);

    // set new model data (after mutation)
    this.model.set(newModel);
  }

  onReadSuccess(value: number, readAll = false) {
    const modelSnapshot = this.model.get();
    const newModel = { ...modelSnapshot, totalNotification: readAll ? 0 : modelSnapshot.totalNotification - value };
    console.log(newModel);

    // set new model data (after mutation)
    this.model.set(newModel);
  }

  list(params: any): Promise<any> {
    return this.restangular.one('notifications').get(params).toPromise();
  }

  read(notificationId: string): Promise<any> {
    return this.restangular.one('notification/read', notificationId).customPOST().toPromise();
  }

  remove(notificationId: string): Promise<any> {
    return this.restangular.one('notification/remove', notificationId).customDELETE().toPromise();
  }

  readAll(): Promise<any> {
    return this.restangular.one('notification/read-all').customPOST().toPromise();
  }

  countUnread(): Promise<any> {
    return this.restangular.one('notifications/count-unread').get().toPromise();
  }

  onReadNotificationSuccess(value: number) {
    this.readNotification.next(value);
  }
}

export interface State {
  totalNotification: number;
}
