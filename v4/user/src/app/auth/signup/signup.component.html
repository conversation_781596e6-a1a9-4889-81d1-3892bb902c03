<div class="d-flex flex-column flex-root">
  <!--begin::Login-->
  <div class="login d-flex flex-column flex-lg-row flex-column-fluid">
    <!--begin::Aside-->
    <div class="login-aside d-flex flex-column flex-row-auto">
      <!--begin::Aside Top-->
      <div class="d-flex flex-column-auto flex-column pt-100">
        <!--begin::Aside header-->
        <a [routerLink]="['/home']" class="login-logo text-center pt-lg-25 pb-10">
          <img
            src="{{ (store.store$ | async)?.config?.siteLogo || '../assets/images/logo/livelearn-header-logo.png' }}"
            class=""
            alt=""
          />
        </a>
        <!--end::Aside header-->
        <!--begin::Aside Title-->
        <p class="mt-4 text-center sub-content" translate>
          <span translate>Join Pinlearn For Free</span> <br />
          <span translate>Explore any interest with 10,000+ classes.</span>
        </p>
        <!--end::Aside Title-->
      </div>
      <!--end::Aside Top-->
      <!--begin::Aside Bottom-->
      <div
        class="aside-img"
        [style]="
          appConfig && appConfig.signupImage
            ? { 'background-image': 'url(' + appConfig.signupImage + ')' }
            : { 'background-image': 'url(' + 'assets/images/login/signup-bg.svg' + ')' }
        "
      ></div>
      <!--end::Aside Bottom-->
    </div>
    <!--begin::Aside-->
    <!--begin::Content-->
    <div class="login-content d-flex justify-content-center align-items-center w-100">
      <!--begin::Wrapper-->
      <div class="d-flex justify-content-center w-100 p-5">
        <!--begin::Signin-->
        <div class="login-form">
          <!--begin::Form-->
          <form class="form" #frm="ngForm" (submit)="submit(frm)">
            <!--begin::Title-->
            <div class="pb-4">
              <h3 class="" translate>Create Account</h3>
              <p class="text-muted sub-content" translate>
                <span translate>Already have an Account</span> ?
                <a [routerLink]="['/auth/login']" class="color-light-red" translate>Sign In</a>
              </p>
            </div>
            <div id="cardDivtutor" class="details">
              <div>
                <h6 translate>I am a :</h6>
                <label class="custom-radio" translate
                  ><span translate>Student</span>
                  <input type="radio" name="type" value="student" [(ngModel)]="account.type" />
                  <span class="checkmark"></span>
                </label>
                <label class="custom-radio"
                  ><span translate>Tutor</span>
                  <input type="radio" name="type" value="tutor" [(ngModel)]="account.type" />
                  <span class="checkmark"></span>
                </label>
              </div>
              <div *ngIf="submitted && account.type === ''">
                <p class="error" translate>Type is required</p>
              </div>
              <span class="text-muted sub-content mb-2" translate>Timezone</span>
              <app-timezone [userTz]="account.timezone" (onChange)="changeTimezone($event)"></app-timezone>
              <div class="form-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="{{ 'Enter name' | translate }}"
                  #name="ngModel"
                  name="name"
                  required
                  [(ngModel)]="account.name"
                />
                <div *ngIf="name.errors && (name.dirty || name.touched || submitted)">
                  <p [hidden]="!name.errors.required" class="error" translate>Name is required</p>
                </div>
              </div>

              <!-- Date of Birth field for students -->
              <div class="form-group" *ngIf="account.type === 'student'">
                <input
                  type="date"
                  class="form-control"
                  placeholder="{{ 'Date of Birth' | translate }}"
                  #dateOfBirth="ngModel"
                  name="dateOfBirth"
                  [(ngModel)]="account.dateOfBirth"
                  (ngModelChange)="onDateOfBirthChange()"
                />
                <small class="form-text text-muted" translate>Please enter your date of birth</small>
              </div>

              <!-- Parental Information for students under 13 -->
              <div *ngIf="showParentalFields" class="parental-info-section">
                <h6 class="text-primary mt-3 mb-3" translate>Parental Information Required</h6>
                <p class="text-muted small" translate>
                  Since you are under 13 years old, we need your parent's information.
                </p>

                <div class="form-group">
                  <input
                    type="text"
                    class="form-control"
                    placeholder="{{ 'Parent Name' | translate }}"
                    #parentName="ngModel"
                    name="parentName"
                    required
                    [(ngModel)]="account.parentName"
                  />
                  <div *ngIf="parentName.errors && (parentName.dirty || parentName.touched || submitted)">
                    <p [hidden]="!parentName.errors.required" class="error" translate>Parent name is required</p>
                  </div>
                </div>

                <div class="form-group">
                  <input
                    type="email"
                    class="form-control"
                    placeholder="{{ 'Parent Email' | translate }}"
                    #parentEmail="ngModel"
                    name="parentEmail"
                    pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$"
                    required
                    [(ngModel)]="account.parentEmail"
                  />
                  <small class="form-text text-muted" translate
                    >We'll use this email to communicate with your parent/guardian.</small
                  >
                  <div *ngIf="parentEmail.errors && (parentEmail.dirty || parentEmail.touched || submitted)">
                    <p [hidden]="!parentEmail.errors.required" class="error" translate>Parent email is required</p>
                    <p [hidden]="!parentEmail.errors.pattern" class="error" translate>Parent email is invalid format</p>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <input
                  id="email-input"
                  type="text"
                  class="form-control"
                  placeholder="{{ 'Enter your email' | translate }}"
                  #email="ngModel"
                  pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$"
                  name="email"
                  required
                  [(ngModel)]="account.email"
                />
                <small class="form-text text-muted" translate>We'll never share your email with anyone else.</small>
                <div *ngIf="email.errors && (email.dirty || email.touched || submitted)">
                  <p [hidden]="!email.errors.required" class="error" translate>Email is required</p>
                  <p [hidden]="!email.errors.pattern" class="error" translate>Email is invalid format</p>
                </div>
              </div>
              <div class="form-group">
                <input
                  type="password"
                  class="form-control"
                  placeholder="{{ 'Password' | translate }}"
                  [minlength]="6"
                  #pw="ngModel"
                  required
                  name="password"
                  [(ngModel)]="account.password"
                />
                <div *ngIf="pw.errors && (pw.dirty || pw.touched || submitted)">
                  <p [hidden]="!pw.errors.minlength" class="error" translate>
                    Password must be from 6 characters length
                  </p>
                  <p [hidden]="!pw.errors.required" class="error" translate>Password is required</p>
                </div>
              </div>
              <div class="form-group">
                <input
                  type="password"
                  class="form-control"
                  placeholder="{{ 'Confirm Password' | translate }}"
                  #cf="ngModel"
                  required
                  name="confirm"
                  [(ngModel)]="confirm.pw"
                />
                <div *ngIf="cf.errors && (cf.dirty || cf.touched || submitted)">
                  <p [hidden]="!cf.errors.required" class="error" translate>Confirm password is required</p>
                </div>
                <div *ngIf="isMath">
                  <p class="error" translate>Confirm password and Password do not match</p>
                </div>
              </div>
              <!-- <p class="text-secondary" *ngIf="account.type === 'tutor'" translate>Upload introduction video</p> -->
              <div class="mt-3" *ngIf="account.type === 'tutor'">
                <p translate>Upload introduction video <span class="text-muted">(Optional)</span></p>
                <label class="custom-radio"
                  ><span translate>Upload</span>
                  <input
                    type="radio"
                    name="videotype"
                    value="upload"
                    (change)="changeUploadType($event)"
                    [(ngModel)]="introVideoType"
                  />
                  <span class="checkmark"></span>
                </label>
                <label class="custom-radio" translate
                  ><span translate>From youtube</span>
                  <input
                    type="radio"
                    name="videotype"
                    value="youtube"
                    (change)="changeUploadType($event)"
                    [(ngModel)]="introVideoType"
                  />
                  <span class="checkmark"></span>
                </label>
              </div>
              <div class="form-group" *ngIf="account.type === 'tutor' && introVideoType === 'youtube'">
                <input
                  type="text"
                  class="form-control"
                  placeholder="{{ 'Youtube Id' | translate }}"
                  #video="ngModel"
                  name="video"
                  [(ngModel)]="accountTutor.introYoutubeId"
                />
                <small translate>Please enter only ID</small>
                <br />
                <small
                  >EG: https://www.youtube.com/watch?v=<strong>xRjlRfpsHGw</strong>
                  <br />
                  ID = <strong>xRjlRfpsHGw</strong></small
                >
                >
              </div>
              <div *ngIf="account.type === 'tutor' && introVideoType === 'upload'">
                <p *ngIf="introVideo && introVideo.name">
                  {{ introVideo.name }}
                </p>
                <app-file-upload [options]="introVideoOptions"></app-file-upload>
                <small translate
                  >Upload mp4 file, webm file, 3gp file, ogg file, wmv file or webm file for your video</small
                >
              </div>

              <div *ngIf="account.type === 'tutor'">
                <p class="text-secondary" translate>
                  Upload verification document<small class="ml-1">
                    (<span translate>Maximum size</span>: {{ maxFileSize }} mb)</small
                  >
                </p>
                <p *ngIf="idDocumentFile && idDocumentFile.name">
                  {{ idDocumentFile.name }}
                </p>
                <app-file-upload [options]="idDocumentOptions"></app-file-upload>
                <small translate
                  >Upload pdf file, docx file, rar file, jpg file, jpeg file, png file or zip file for your
                  document</small
                >
              </div>

              <div *ngIf="account.type === 'tutor'">
                <p class="text-secondary" translate>
                  Upload resume document <span class="text-muted">(Optional)</span
                  ><small class="ml-1" translate> (<span translate>Maximum size</span>: {{ maxFileSize }} mb)</small>
                </p>
                <p *ngIf="resumeFile && resumeFile.name">{{ resumeFile.name }}</p>
                <app-file-upload [options]="resumeOptions"></app-file-upload>
                <small translate>Upload pdf file for your document</small>
              </div>

              <div *ngIf="account.type === 'tutor'">
                <p class="text-secondary" translate>
                  Upload certification document <span class="text-muted">(Optional)</span
                  ><small class="ml-1"> (<span translate>Maximum size</span>: {{ maxFileSize }} mb)</small>
                </p>
                <p *ngIf="certificationFile && certificationFile.name">
                  {{ certificationFile.name }}
                </p>
                <app-file-upload [options]="certificationOptions"></app-file-upload>
                <small translate>Upload pdf file for your document</small>
              </div>
            </div>
            <div class="mt-3 ml-4">
              <input class="form-check-input" type="checkbox" [(ngModel)]="agree" name="accept" value="accept" />
              <label class="text-secondary" translate for="accept">
                I agree to the website terms & conditions and privacy policy
              </label>
            </div>
            <div class="text-center">
              <button class="btn btn-default mt-5 btn-block" type="submit" translate [disabled]="loading || !agree">
                Sign Up
              </button>
            </div>
            <!--end::Form group-->
            <!--begin::Action-->
            <!--end::Action-->
          </form>
          <!--end::Form-->
        </div>
        <!--end::Signin-->
      </div>
      <!--end::Wrapper-->
    </div>
    <!--end::Content-->
  </div>
  <!--end::Login-->
</div>
<!--end::Main-->
