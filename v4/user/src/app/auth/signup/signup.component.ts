import { SeoService } from './../../shared/services/seo.service';
import { AfterViewInit, Component } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../shared/services';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from '../../app.service';
import { StoreService } from '../../shared/services/store.service';

@Component({
  templateUrl: 'signup.component.html'
})
export class SignupComponent implements AfterViewInit {
  public account: any = {
    email: '',
    password: '',
    name: '',
    type: '',
    timezone: '',
    dateOfBirth: '',
    parentName: '',
    parentEmail: ''
  };
  public accountTutor: any = {
    email: '',
    password: '',
    name: '',
    issueDocument: '',
    resumeDocument: null,
    certificationDocument: null,
    timezone: '',
    introVideoId: null,
    introYoutubeId: null
  };
  public introVideoType: string = 'upload';
  public confirm: any = {
    pw: ''
  };
  public maxFileSize: number;
  public isMath: boolean = false;
  public submitted: boolean = false;
  public idDocumentOptions: any = {};
  public resumeOptions: any = {};
  public certificationOptions: any = {};
  public introVideoOptions: any = {};
  public idDocumentFile: any;
  public resumeFile: any;
  public certificationFile: any;
  public introVideo: any;
  public appConfig: any;
  public loading: boolean = false;
  public agree: boolean = true;
  public showParentalFields: boolean = false;

  constructor(
    private auth: AuthService,
    public router: Router,
    private toasty: ToastrService,
    private route: ActivatedRoute,
    private seoService: SeoService,
    private translate: TranslateService,
    private appService: AppService,
    public store: StoreService
  ) {
    this.maxFileSize = this.appService.settings.maximumFileSize;
    this.appConfig = this.route.snapshot.data.appConfig;
    if (this.appConfig) {
      let title = this.appConfig.siteName + ' - Sign Up';
      seoService.update(title);
    }

    this.introVideoOptions = {
      url: this.appService.settings.apiBaseUrl + '/tutors/upload-introVideo',
      onCompleteItem: resp => {
        if (resp && resp.data) {
          this.accountTutor.introVideoId = resp.data._id;
        }
        this.loading = false;
      },
      onFileSelect: resp => {
        const lastIndex = resp.length - 1;
        const file = resp[lastIndex].file;
        const ext = file.name.split('.').pop().toLowerCase();
        if (['mp4', 'webm', '3gp', 'ogg', 'wmv', 'webm'].indexOf(ext) === -1) {
          this.introVideoOptions.uploader.clearQueue();
          return this.toasty.error(this.translate.instant('Invalid file type'));
        }
        this.introVideo = file;
      },
      uploadOnSelect: true,
      id: 'id-introVideo',
      onUploading: resp => (this.loading = true)
    };

    this.idDocumentOptions = {
      url: this.appService.settings.apiBaseUrl + '/tutors/upload-document',
      onCompleteItem: resp => {
        if (resp && resp.data) {
          this.accountTutor.issueDocument = resp.data._id;
        }
        this.loading = false;
      },
      onFileSelect: resp => {
        const lastIndex = resp.length - 1;
        const file = resp[lastIndex].file;
        const ext = file.name.split('.').pop().toLowerCase();
        if (['pdf', 'doc', 'docx', 'zip', 'rar', 'jpg', 'jpeg', 'png'].indexOf(ext) === -1) {
          this.idDocumentOptions.uploader.clearQueue();
          return this.toasty.error(this.translate.instant('Invalid file type'));
        }
        this.idDocumentFile = file;
      },
      uploadOnSelect: true,
      id: 'id-document',
      onUploading: resp => (this.loading = true)
    };
    this.resumeOptions = {
      url: this.appService.settings.apiBaseUrl + '/tutors/upload-document',
      onCompleteItem: resp => {
        this.accountTutor.resumeDocument = resp.data._id;
        this.loading = false;
      },
      onFileSelect: resp => {
        const lastIndex = resp.length - 1;
        const file = resp[lastIndex].file;
        const ext = file.name.split('.').pop().toLowerCase();
        if (['pdf'].indexOf(ext) === -1) {
          this.resumeOptions.uploader.clearQueue();
          return this.toasty.error(this.translate.instant('Invalid file type'));
        }
        this.resumeFile = file;
      },
      uploadOnSelect: true,
      id: 'id-resume',
      onUploading: resp => (this.loading = true)
    };
    this.certificationOptions = {
      url: this.appService.settings.apiBaseUrl + '/tutors/upload-document',
      onCompleteItem: resp => {
        this.accountTutor.certificationDocument = resp.data._id;
        this.loading = false;
      },
      onFileSelect: resp => {
        const lastIndex = resp.length - 1;
        const file = resp[lastIndex].file;
        const ext = file.name.split('.').pop().toLowerCase();
        if (['pdf'].indexOf(ext) === -1) {
          this.certificationOptions.uploader.clearQueue();
          return this.toasty.error(this.translate.instant('Invalid file type'));
        }
        this.certificationFile = file;
      },
      uploadOnSelect: true,
      id: 'id-verification',
      onUploading: resp => (this.loading = true)
    };
  }

  public onlyNumberKey(event) {
    return event.charCode === 8 || event.charCode === 0 ? null : event.charCode >= 48 && event.charCode <= 57;
  }

  public onDateOfBirthChange() {
    if (this.account.type === 'student' && this.account.dateOfBirth) {
      const birthDate = new Date(this.account.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Calculate exact age
      const exactAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate()) ? age - 1 : age;

      this.showParentalFields = exactAge < 13;

      // Clear parental fields if not needed
      if (!this.showParentalFields) {
        this.account.parentName = '';
        this.account.parentEmail = '';
      }
    } else {
      this.showParentalFields = false;
      this.account.parentName = '';
      this.account.parentEmail = '';
    }
  }

  public async submit(frm: any) {
    this.submitted = true;
    if (frm.invalid) {
      return;
    }
    if (!this.account.timezone) {
      return this.toasty.error(this.translate.instant('Please select timezone'));
    }
    if (this.account.password !== this.confirm.pw) {
      this.isMath = true;
      return this.toasty.error(this.translate.instant('Confirm password and password dont match'));
    }
    if (this.account.type === '') {
      return this.toasty.error(this.translate.instant('Please select type'));
    }

    // Validate parental information for students under 13
    if (this.showParentalFields) {
      if (!this.account.parentName || this.account.parentName.trim() === '') {
        return this.toasty.error(this.translate.instant('Parent name is required for students under 13 years old'));
      }
      if (!this.account.parentEmail || this.account.parentEmail.trim() === '') {
        return this.toasty.error(this.translate.instant('Parent email is required for students under 13 years old'));
      }
    }

    this.account.email = this.account.email.toLowerCase();

    if (this.account.type === 'tutor') {
      this.accountTutor.name = this.account.name;
      this.accountTutor.email = this.account.email;
      this.accountTutor.password = this.account.password;
      this.accountTutor.timezone = this.account.timezone;
      if (this.introVideoType === 'youtube') {
        this.accountTutor.introVideoId = null;
      }

      // Clean up empty optional fields
      if (!this.accountTutor.resumeDocument || this.accountTutor.resumeDocument === '') {
        this.accountTutor.resumeDocument = null;
      }
      if (!this.accountTutor.certificationDocument || this.accountTutor.certificationDocument === '') {
        this.accountTutor.certificationDocument = null;
      }
      if (!this.accountTutor.introVideoId || this.accountTutor.introVideoId === '') {
        this.accountTutor.introVideoId = null;
      }
      if (!this.accountTutor.introYoutubeId || this.accountTutor.introYoutubeId === '') {
        this.accountTutor.introYoutubeId = null;
      }

      if (!this.accountTutor.issueDocument) {
        return this.toasty.error(this.translate.instant('Please upload verification document'));
      }
      return this.auth
        .registerTutor(this.accountTutor)
        .then(resp => {
          this.toasty.success(
            this.translate.instant('Your account has been created, please verify your email then login')
          );
          this.router.navigate(['/auth/login']);
        })
        .catch(err => this.toasty.error(this.translate.instant(err.data.message)));
    }
    this.auth
      .register(this.account)
      .then(resp => {
        this.toasty.success(
          this.translate.instant('Your account has been created, please verify your email then login')
        );
        this.router.navigate(['/auth/login']);
      })
      .catch(err => this.toasty.error(this.translate.instant(err.data.data.message)));
  }

  changeTimezone(event) {
    if (event === 'Asia/Saigon') {
      this.account.timezone = 'Asia/Ho_Chi_Minh';
    } else {
      this.account.timezone = event;
    }
  }

  changeUploadType(event) {
    if (event.target.value === 'youtube') {
      this.accountTutor.introYoutubeId = null;
    } else {
      this.accountTutor.introYoutubeId = null;
    }
  }

  ngAfterViewInit() {
    const target = document.getElementById('email-input');
    target.addEventListener(
      'paste',
      (event: any) => {
        event.preventDefault();
        const clipboard = event.clipboardData,
          text = clipboard.getData('Text');
        event.target.value = text.replace(/ /g, '');
        this.account.email = text.replace(/ /g, '');
      },
      false
    );
  }
}
