import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Simple state management service to replace @angular-extensions/model
 * Provides the same API but with modern Angular patterns
 */
export class Model<T> {
  private _data$ = new BehaviorSubject<T>(this.initialData);
  
  constructor(private initialData: T) {}

  get data$(): Observable<T> {
    return this._data$.asObservable();
  }

  get(): T {
    return this._data$.value;
  }

  set(data: T): void {
    // Create immutable copy to prevent accidental mutations
    const immutableData = JSON.parse(JSON.stringify(data));
    this._data$.next(immutableData);
  }
}

@Injectable({
  providedIn: 'root'
})
export class ModelFactory<T> {
  create(initialData: T): Model<T> {
    return new Model<T>(initialData);
  }

  createMutable(initialData: T): Model<T> {
    // For compatibility - same as create but could be optimized later
    return new Model<T>(initialData);
  }
}
