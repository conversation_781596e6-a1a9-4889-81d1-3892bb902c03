import { Injectable } from '@angular/core';
import { Model, ModelFactory } from './simple-state.service';
import { Observable, BehaviorSubject } from 'rxjs';
import { IUser } from '../../user/interface';

@Injectable({
  providedIn: 'root'
})
export class NotificatioCountService {
  public model: Model<State>;
  store$: Observable<State>;

  constructor(private modelFactory: ModelFactory<State>) {
    this.model = this.modelFactory.create({
      totalNotification: null
    });
    this.store$ = this.model.data$;
  }

  update(value: any) {
    // retrieve raw model data
    const modelSnapshot = this.model.get();

    // mutate model data
    const newModel = { ...modelSnapshot, totalNotification: value };

    // set new model data (after mutation)
    this.model.set(newModel);
  }

  read(readAll = false) {
    // retrieve raw model data
    const modelSnapshot = this.model.get();

    // mutate model data
    const newModel = { ...modelSnapshot, totalNotification: readAll ? 0 : modelSnapshot.totalNotification - 1 };

    // set new model data (after mutation)
    this.model.set(newModel);
  }
}

export interface State {
  totalNotification: number;
}
