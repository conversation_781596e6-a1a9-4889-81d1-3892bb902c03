import { Injectable } from '@angular/core';
import { Model, ModelFactory } from './simple-state.service';
import { Observable, BehaviorSubject } from 'rxjs';
import { IUser } from '../../user/interface';

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  public model: Model<State>;
  store$: Observable<State>;

  constructor(private modelFactory: ModelFactory<State>) {
    this.model = this.modelFactory.create({
      config: {},
      current: null
    });
    this.store$ = this.model.data$;
  }

  update(stateUpdates: any) {
    // retrieve raw model data
    const modelSnapshot = this.model.get();

    // mutate model data
    const newModel = { ...modelSnapshot, ...stateUpdates };

    // set new model data (after mutation)
    this.model.set(newModel);
  }

  showBooking(): boolean {
    const current = this.model.get().current;
    if (!current || (current && current.type === 'student')) return true;
    const config = this.model.get().config;
    return current && current.type === 'tutor' && config.allowTutorBooking ? true : false;
  }
}

export interface State {
  config: any;
  current: IUser;
}
