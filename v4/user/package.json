{"name": "app-frontend", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "obfuscator": "npm run build && npx javascript-obfuscator ./dist --exclude='dist/assets/*' --output ./obfuscator && rsync --ignore-existing --recursive ./dist ./obfuscator"}, "private": true, "dependencies": {"@angular/animations": "^10.0.11", "@angular/cdk": "^11.2.12", "@angular/common": "^10.0.11", "@angular/compiler": "^10.0.11", "@angular/core": "^10.0.11", "@angular/forms": "^10.0.11", "@angular/http": "^7.2.16", "@angular/localize": "^10.0.11", "@angular/platform-browser": "^10.0.11", "@angular/platform-browser-dynamic": "^10.0.11", "@angular/router": "^10.0.11", "@ckeditor/ckeditor5-angular": "^2.0.1", "@ckeditor/ckeditor5-build-classic": "^21.0.0", "@ckeditor/ckeditor5-image": "^23.0.0", "@flaticon/flaticon-uicons": "^2.4.0", "@fortawesome/angular-fontawesome": "^0.7.0", "@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/free-brands-svg-icons": "^5.14.0", "@fortawesome/free-solid-svg-icons": "^5.14.0", "@fullcalendar/angular": "^5.11.0", "@fullcalendar/daygrid": "^5.11.0", "@fullcalendar/interaction": "^5.11.0", "@fullcalendar/list": "^5.11.0", "@fullcalendar/moment": "^5.11.0", "@fullcalendar/timegrid": "^5.11.0", "@ng-bootstrap/ng-bootstrap": "^7.0.0", "@ng-select/ng-select": "^5.0.1", "@ngx-share/button": "^7.1.4", "@ngx-share/buttons": "^7.1.4", "@ngx-share/core": "^7.1.4", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@stripe/stripe-js": "^3.0.0", "@swimlane/ngx-charts": "^17.0.1", "@types/jquery": "^3.3.0", "angular-froala-wysiwyg": "^3.2.1-1", "angular-svg-icon": "^10.0.0", "bootstrap": "^4.5.2", "core-js": "^3.6.5", "dom-to-image": "^2.6.0", "flexslider": "^2.7.2", "fullcalendar": "^5.11.0", "jasmine": "^3.6.1", "javascript-obfuscator": "^4.0.0", "jquery": "^3.5.1", "js-cookie": "^3.0.1", "jspdf": "^2.3.1", "lodash": "^4.17.20", "moment": "^2.27.0", "moment-range": "^4.0.2", "moment-timezone": "0.5.31", "ng-circle-progress": "^1.6.0", "ng-multiselect-dropdown": "^0.2.10", "ng2-dragula": "^2.1.1", "ng2-file-upload": "^4.0.0", "ng2-smart-table": "^1.6.0", "ng2-timezone-selector": "^0.2.4", "ngx-bootstrap": "^5.6.1", "ngx-daterangepicker-material": "^4.0.1", "ngx-editor": "^5.0.0", "ngx-extended-pdf-viewer": "^6.1.0", "ngx-quill": "^12.0.1", "ngx-restangular": "^6.0.0", "ngx-sharebuttons": "^8.0.5", "ngx-slick-carousel": "^0.5.1", "ngx-smart-modal": "^7.4.1", "ngx-socket-io": "^3.2.0", "ngx-stripe": "15.8.1", "ngx-toastr": "^13.0.0", "pace-js": "^1.0.2", "popper.js": "^1.16.1", "quill": "^1.3.7", "rxjs": "^6.2.0", "rxjs-compat": "^6.0.0-rc.0", "select2": "^4.0.13", "slick-carousel": "^1.8.1", "tooltip.js": "^1.3.3", "tslib": "^2.0.1", "zone.js": "^0.11.1"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1000.7", "@angular/cli": "^10.0.7", "@angular/compiler-cli": "^10.0.11", "@angular/language-service": "^10.0.11", "@types/chartist": "^0.11.0", "@types/jasmine": "~3.5.12", "@types/jasminewd2": "~2.0.8", "@types/jquery": "^3.5.1", "@types/lodash": "^4.14.195", "@types/node": "^14.6.0", "@types/prosemirror-model": "^1.17.0", "@types/prosemirror-state": "^1.4.0", "@types/prosemirror-view": "^1.24.0", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.2", "karma": "~5.1.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "~2.0.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "node-sass": "^4.14.1", "protractor": "~7.0.0", "ts-node": "~8.10.2", "tslint": "~6.1.3", "typescript": "^3.9.7"}, "volta": {"node": "10.24.1"}}