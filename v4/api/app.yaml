runtime: nodejs22

# Service configuration
service: api

# Instance configuration
instance_class: F2
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# Environment variables
env_variables:
  NODE_ENV: production
  APP_NAME: LIVELEARN
  ALLOW_CORS: true
  PORT: 8080
  VERSION: regular

  # Service URLs
  baseUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/
  userWebUrl: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  adminWebUrl: https://admin-dot-vocabulous-466003.et.r.appspot.com
  adminURL: https://admin-dot-vocabulous-466003.et.r.appspot.com

  # Site configuration
  SITE_NAME: LIVELEARN
  logoUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/assets/logo.png

  # CORS and security
  host: vocabulous-466003.et.r.appspot.com

  # Session configuration (replace with your actual secret)
  SESSION_SECRET: VocabulousSecureSessionKey2024RandomString123456789

  # File size limits (in MB)
  MAX_PHOTO_SIZE: 10
  MAX_VIDEO_SIZE: 200
  MAX_FILE_SIZE: 100

  # File storage configuration
  USE_S3: true
  AWS_S3_BUCKET: vocabulous-media-storage

  # Social links (update with your actual URLs)
  facebookUrl: https://facebook.com/yourpage
  twitterUrl: https://twitter.com/yourhandle

  # Database connections (REQUIRED - update with your actual values)
  # MONGO_URI: mongodb+srv://username:<EMAIL>/livelearn
  MONGO_URI: mongodb+srv://ollieshing:<EMAIL>/livelearn

  # Redis configuration (REQUIRED - update with your actual values)
  # REDIS_HOST: redis-12345.c1.us-central1-1.gce.cloud.redislabs.com
  # REDIS_PORT: 12345
  # REDIS_PASS: your-redis-password
  # REDIS_URL: redis://default:password@host:port
  REDIS_HOST: redis-15686.c334.asia-southeast2-1.gce.redns.redis-cloud.com
  REDIS_PORT: 15686
  REDIS_DB: 0
  REDIS_PASS: qssl4qzknezoR2Z1WMTBPvTjYR2RsjIe
  REDIS_URL: redis://default:<EMAIL>:15686

  # Queue configuration
  QUEUE_PREFIX: queueCourse

  # Email configuration (update with your actual values)
  SENDGRID_API_KEY: SG.placeholder-key-replace-with-actual-sendgrid-key
  MAIL_SERVICE: sendgrid
  mailFrom: <EMAIL>
  ADMIN_EMAIL: <EMAIL>

# Health check configuration
readiness_check:
  path: '/health'
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: '/health'
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Network configuration
network:
  forwarded_ports:
    - 8080

# Static file handlers for public assets
handlers:
  # Handle static files
  - url: /photos
    static_dir: public/photos
    secure: always

  - url: /videos
    static_dir: public/videos
    secure: always

  - url: /audios
    static_dir: public/audios
    secure: always

  - url: /files
    static_dir: public/files
    secure: always

  - url: /assets
    static_dir: public/assets
    secure: always

  - url: /docs
    static_dir: apidocs
    secure: always

  # Handle all other requests with the Node.js app
  - url: /.*
    script: auto
    secure: always
