{"name": "student-tutor-live-learn-api", "version": "4.0.0", "description": "API for student-tutor-live-learn", "main": "server/www.js", "dependencies": {"agenda": "^5.0.0", "async": "^2.6.1", "aws-sdk": "^2.355.0", "axios": "^0.27.2", "base64-img": "^1.0.4", "bee-queue": "^1.2.2", "body-parser": "^1.18.2", "composable-middleware": "^0.3.0", "cors": "^2.8.4", "dotenv": "^5.0.1", "es6-promisify": "^6.0.0", "exceljs": "^4.2.1", "express": "^4.16.3", "express-jwt": "^5.3.1", "fluent-ffmpeg": "^2.1.2", "gm": "^1.23.1", "http-status-codes": "^2.1.4", "joi": "^13.3.0", "json2csv": "^5.0.6", "jsonwebtoken": "^8.2.1", "jsrsasign": "^10.5.27", "lodash": "^4.17.10", "method-override": "^2.3.10", "migrate": "^1.8.0", "mkdirp": "^0.5.1", "moment": "^2.22.1", "mongoose": "^5.0.18", "morgan": "^1.9.0", "multer": "^1.3.0", "nconf": "^0.10.0", "nodemailer": "^4.6.5", "nodemailer-pepipost-transport": "^0.4.0", "nodemailer-sendgrid-transport": "^0.2.0", "nodemailer-sparkpost-transport": "^2.0.0", "nodemon": "^1.19.4", "passport": "^0.4.0", "passport-local": "^1.0.0", "paydunya": "^1.0.11", "postal-codes-js": "^2.5.2", "query-string": "^7.1.1", "request": "^2.87.0", "socket.io": "^2.3.0", "socket.io-redis": "^5.4.0", "stripe": "^6.31.0", "swig": "^1.4.2", "synp": "^1.9.13"}, "devDependencies": {"chai": "^4.1.2", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.12.0", "istanbul": "^0.4.5", "mocha": "^5.2.0", "supertest": "^3.1.0"}, "scripts": {"start": "node ./server/www.js", "test": "NODE_ENV=test ./node_modules/mocha/bin/_mocha --timeout 100000", "dev": "NODE_ENV=development && nodemon -- ./server/www.js -e js,json", "migrate": "node server/migrate", "migrate:create": "./node_modules/.bin/migrate create"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "private", "volta": {"node": "22.16.0"}, "engines": {"node": "22.x"}}