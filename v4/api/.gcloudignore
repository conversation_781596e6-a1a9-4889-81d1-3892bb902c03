# This file specifies files that are intentionally untracked by gcloud
# and should not be uploaded to Google App Engine

# Node.js dependencies
node_modules/

# Development and testing files
test/
coverage/
*.test.js
*.spec.js

# Environment files
.env
.env.*
!.env.example

# Version control
.git/
.gitignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
README.md
*.md
docs/

# Package files (keep package.json but ignore lock files)
package-lock.json
yarn.lock

# Build artifacts
build/
dist/
.tmp/

# Temporary files
tmp/
temp/

# OS generated files
Thumbs.db
.DS_Store

# API documentation
apidocs/

# Migration files (if you want to exclude them)
migrations/

# Custom public folders that might be large
public/avatar/
public/photos/
public/videos/
public/documents/
public/files/
public/audios/

# Configuration files that shouldn't be deployed
server/config/local.json
server/config/development.json
server/config/test.json

# Backup files
*.bak
*.backup
