const express = require('express');
const { existsSync } = require('fs');
const { join } = require('path');
const compression = require('compression');

function createServer() {
  const server = express();
  server.use(compression({ level: 8 }));

  const distFolder = join(process.cwd(), 'dist/browser');
  console.log(`Looking for dist folder at: ${distFolder}`);
  console.log(`Dist folder exists: ${existsSync(distFolder)}`);

  // Health check endpoint for Google App Engine
  server.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // Serve static files from /browser
  server.get(
    '*.*',
    express.static(distFolder, {
      maxAge: '1y'
    })
  );

  // All routes serve the index.html file (SPA mode)
  server.get('*', (req, res) => {
    const indexPath = join(distFolder, 'index.html');
    if (existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      res.status(404).send('Application not found');
    }
  });

  return server;
}

function run() {
  const port = process.env.PORT || 8080;

  const server = createServer();

  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
    console.log(`Environment: ${process.env.NODE_ENV}`);
    console.log(`Server started successfully at ${new Date().toISOString()}`);
  });

  // Handle server errors
  server.on('error', (error) => {
    console.error('Server error:', error);
  });
}

// Check if this is the main module
if (require.main === module) {
  run();
}

module.exports = { createServer };
