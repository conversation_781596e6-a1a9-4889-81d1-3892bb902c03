const express = require('express');
const { existsSync } = require('fs');
const { join } = require('path');
const compression = require('compression');

// Import zone.js for Angular Universal
require('zone.js/node');
require('reflect-metadata');

// Dynamic imports for ES modules
async function createServer() {
  try {
    // Dynamic import for ES modules
    const { APP_BASE_HREF } = await import('@angular/common');
    const { ngExpressEngine } = await import('@nguniversal/express-engine');
    const { AppServerModule } = await import('./src/app/app.server.module.js');

    const server = express();
    server.use(compression({ level: 8 }));
    
    const distFolder = join(process.cwd(), 'dist/browser');
    console.log(`Looking for dist folder at: ${distFolder}`);
    console.log(`Dist folder exists: ${existsSync(distFolder)}`);
    
    const indexHtml = existsSync(join(distFolder, 'index.original.html'))
      ? 'index.original.html'
      : 'index';
    console.log(`Using index file: ${indexHtml}`);

    // Our Universal express-engine
    server.engine('html', ngExpressEngine({
      inlineCriticalCss: false,
      bootstrap: AppServerModule
    }));

    server.set('view engine', 'html');
    server.set('views', distFolder);

    // Health check endpoint for Google App Engine
    server.get('/health', (req, res) => {
      res
        .status(200)
        .json({ status: 'ok', timestamp: new Date().toISOString() });
    });

    // Serve static files from /browser
    server.get('*.*', express.static(distFolder, {
      maxAge: '1y'
    }));

    // Specific routes for users
    const routesPath = ['/users', '/users/**'];
    server.get(routesPath, (req, res) => {
      res.sendFile(distFolder + '/index.html');
    });

    // All regular routes use the Universal engine
    server.get('*', (req, res) => {
      res.render(indexHtml, {
        req,
        providers: [
          { provide: APP_BASE_HREF, useValue: req.baseUrl },
          { provide: 'REQUEST', useValue: req },
          { provide: 'RESPONSE', useValue: res }
        ]
      });
    });

    return server;
  } catch (error) {
    console.error('Error setting up Express app:', error);
    throw error;
  }
}

function run() {
  const port = process.env.PORT || 8080;
  
  createServer().then(server => {
    server.listen(port, () => {
      console.log(`Node Express server listening on http://localhost:${port}`);
      console.log(`Environment: ${process.env.NODE_ENV}`);
      console.log(`Server started successfully at ${new Date().toISOString()}`);
    });

    // Handle server errors
    server.on('error', (error) => {
      console.error('Server error:', error);
    });
  }).catch(error => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

// Check if this is the main module
if (require.main === module) {
  run();
}

module.exports = { createServer };
