{"name": "front-office-pin-learn", "version": "4.0.0", "license": "private", "scripts": {"ng": "ng", "start": "node server-production.js", "build": "ng build", "gcp-build": "echo 'Build completed locally, skipping cloud build'", "watch": "ng build --watch --configuration development", "test": "ng test", "coverage": "ng test --no-watch --code-coverage", "lint": "ng lint", "dev:ssr": "ng run scss-angular:serve-ssr", "serve:ssr": "node dist/server/main.js", "build:ssr": "npm install --legacy-peer-deps && ng build && ng run scss-angular:server", "prerender": "ng run scss-angular:prerender"}, "private": true, "dependencies": {"@angular/animations": "15.2.3", "@angular/cdk": "15.2.3", "@angular/common": "15.2.3", "@angular/compiler": "15.2.3", "@angular/core": "15.2.3", "@angular/forms": "15.2.3", "@angular/localize": "15.2.3", "@angular/platform-browser": "15.2.3", "@angular/platform-browser-dynamic": "15.2.3", "@angular/platform-server": "15.2.3", "@angular/router": "15.2.3", "@angular/service-worker": "15.2.3", "@flaticon/flaticon-uicons": "^2.4.0", "@fortawesome/angular-fontawesome": "0.12.0", "@fortawesome/fontawesome-free": "6.3.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fullcalendar/angular": "^6.1.9", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/moment": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@ng-bootstrap/ng-bootstrap": "14", "@ng-select/ng-select": "^10.0.4", "@nguniversal/express-engine": "15.2.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.11.8", "@stripe/stripe-js": "^3.0.0", "@swimlane/ngx-charts": "^20.4.1", "bootstrap": "5.2.3", "compression": "^1.7.4", "crypto-js": "^4.2.0", "dom-to-image-more": "^3.2.0", "express": "4.18.2", "flexslider": "^2.7.2", "jquery": "^3.7.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ng2-file-upload": "^4.0.0", "ngx-cookie-service": "^15.0.0", "ngx-cookie-service-ssr": "^15.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-extended-pdf-viewer": "^15.0.0", "ngx-quill": "20.0.1", "ngx-sharebuttons": "12.0.0", "ngx-slick-carousel": "^15.0.0", "ngx-socket-io": "4.4.0", "ngx-stripe": "15.8.1", "ngx-toastr": "16.2.0", "pdfjs-dist": "2.11.338", "prismjs": "1.29.0", "quill": "^1.3.7", "rxjs": "7.8.0", "slick-carousel": "^1.8.1", "tooltip.js": "^1.3.3", "tslib": "2.5.0", "webpack": "^5.90.3", "zone.js": "0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "15.2.4", "@angular-eslint/builder": "15.2.1", "@angular-eslint/eslint-plugin": "15.2.1", "@angular-eslint/eslint-plugin-template": "15.2.1", "@angular-eslint/schematics": "15.2.1", "@angular-eslint/template-parser": "15.2.1", "@angular/cli": "15.2.4", "@angular/compiler-cli": "15.2.3", "@nguniversal/builders": "15.2.0", "@types/compression": "^1.7.4", "@types/crypto-js": "^4.2.2", "@types/express": "4.17.17", "@types/jasmine": "4.3.1", "@types/jquery": "^3.5.24", "@types/lodash": "^4.14.200", "@types/node": "18.15.3", "@types/quill": "1.3.7", "@typescript-eslint/eslint-plugin": "5.48.2", "@typescript-eslint/parser": "5.48.2", "eslint": "^8.33.0", "jasmine-core": "4.6.0", "karma": "6.4.1", "karma-chrome-launcher": "3.1.1", "karma-coverage": "2.2.0", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.0.0", "typescript": "4.8.2"}, "volta": {"node": "16.20.2"}}