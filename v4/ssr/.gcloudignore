# This file specifies files that are intentionally untracked by gcloud
# and should not be uploaded to Google App Engine

# Node.js dependencies (will be installed during build)
node_modules/

# Angular cache directories
.angular/
.angular/cache/

# Build output directories (keep dist/ for deployment)
# dist/
tmp/

# Cache and temporary files
*.tmp
*.temp
.cache/

# Build locally and upload only necessary files
src/
*.ts
!server-production.js
angular.json
tsconfig*.json
server.ts

# Keep only essential files for runtime
# dist/ (commented out to include built files)
# package.json (needed for dependencies)
# app.yaml (needed for App Engine)

# Development and testing files
test/
coverage/
*.test.ts
*.spec.ts
karma.conf.js

# Environment files
.env
.env.*
!.env.example

# Version control
.git/
.gitignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Documentation
README.md
*.md

# Package files (keep package.json but ignore lock files)
package-lock.json
yarn.lock

# Keep Angular configuration files for building
# angular.json
# tsconfig*.json
# ngsw-config.json

# Build artifacts (except dist which we want to deploy)
build/
.tmp/

# Temporary files
tmp/
temp/

# OS generated files
Thumbs.db
.DS_Store

# Backup files
*.bak
*.backup

# Server-side rendering specific
server.ts
