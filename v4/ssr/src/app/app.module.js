var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { BrowserModule } from '@angular/platform-browser';
import { APP_INITIALIZER, NgModule, isDevMode } from '@angular/core';
import { AppComponent } from './app.component';
import { NotFoundComponent } from './modules/general/not-found/not-found.component';
import { AppRoutingModule } from './app-routing.module';
import { ServiceWorkerModule } from '@angular/service-worker';
import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { HeaderModule } from './components/header/header.module';
import { FooterModule } from './components/footer/footer.module';
import { AppService } from './services/app-service';
import { ToastrModule } from 'ngx-toastr';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AuthService, InterceptorService, SystemService } from './services';
import { FullComponent } from './layouts/full/full.component';
import { BlankComponent } from './layouts/blank/blank.component';
import { CookieService } from 'ngx-cookie-service';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HomeModule } from './modules/home/<USER>';
import { SharedModule } from './shared.module';
import { NgOptimizedImage } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SpinnerComponent } from './components/uis/spinner.component';
import { environment } from 'src/environments/environment';
import { DashboardLayoutComponent } from './layouts/dashboard/dashboard.component';
import { SocketIoModule } from 'ngx-socket-io';
const config = {
    url: environment.socketUrl,
    options: { query: {}, transports: ['websocket'], autoConnect: false }
};
export function createTranslateLoader(http) {
    return new TranslateHttpLoader(http, `${environment.apiBaseUrl}/i18n/`, '.json');
    // return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
let AppModule = class AppModule {
};
AppModule = __decorate([
    NgModule({
        declarations: [
            AppComponent,
            NotFoundComponent,
            FullComponent,
            BlankComponent,
            SpinnerComponent,
            DashboardLayoutComponent
        ],
        imports: [
            BrowserModule.withServerTransition({ appId: 'livelearn-ssr-app' }),
            AppRoutingModule,
            HttpClientModule,
            BrowserAnimationsModule,
            HeaderModule,
            FooterModule,
            ServiceWorkerModule.register('ngsw-worker.js', {
                enabled: !isDevMode(),
                // Register the ServiceWorker as soon as the application is stable
                // or after 30 seconds (whichever comes first).
                registrationStrategy: 'registerWhenStable:30000'
            }),
            ToastrModule.forRoot({
                timeOut: 5000,
                positionClass: 'toast-bottom-right',
                preventDuplicates: true
            }),
            TranslateModule.forRoot({
                loader: {
                    provide: TranslateLoader,
                    useFactory: createTranslateLoader,
                    deps: [HttpClient]
                }
            }),
            NgbModule,
            HomeModule,
            SharedModule,
            NgOptimizedImage,
            SocketIoModule.forRoot(config)
        ],
        providers: [
            {
                provide: APP_INITIALIZER,
                useFactory: (app, auth, system) => async () => await app.getData().then(async () => {
                    await system.configs();
                    await auth.initAppGetCurrentUser();
                }),
                deps: [AppService, AuthService, SystemService],
                multi: true
            },
            {
                provide: HTTP_INTERCEPTORS,
                useClass: InterceptorService,
                multi: true
            },
            CookieService,
            SsrCookieService
            // SocketService
        ],
        bootstrap: [AppComponent]
    })
], AppModule);
export { AppModule };
