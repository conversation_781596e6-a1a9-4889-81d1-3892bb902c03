var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Pipe } from '@angular/core';
let AppCurrencyPipe = class AppCurrencyPipe {
    constructor(currencyPipe) {
        this.currencyPipe = currencyPipe;
    }
    transform(value, currencyCode, display, digitsInfo, locale) {
        const currencySymbol = localStorage.getItem('currencySymbol') || '$';
        if (value != null) {
            const formattedValue = this.currencyPipe.transform(value, currencyCode ?? currencySymbol, display, digitsInfo, locale);
            return formattedValue?.replace('.00', '') || '';
        }
        return (this.currencyPipe
            ?.transform(0, currencyCode ?? currencySymbol, display, locale)
            ?.split('0.00')[0] || '');
    }
};
AppCurrencyPipe = __decorate([
    Pipe({ name: 'appCurrency' })
], AppCurrencyPipe);
export { AppCurrencyPipe };
