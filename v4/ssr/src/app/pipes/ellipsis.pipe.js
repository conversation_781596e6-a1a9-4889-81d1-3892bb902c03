var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Pipe } from '@angular/core';
let EllipsisPipe = class EllipsisPipe {
    constructor() { }
    transform(value, args) {
        if (!args) {
            return value;
        }
        if (value && value.length > args) {
            return value.substring(0, args) + '...';
        }
        else {
            return value;
        }
        // if (!args) {
        //   return this.sanitizer.bypassSecurityTrustHtml(value);
        // }
        // if (value && value.length > args) {
        //   return this.sanitizer.bypassSecurityTrustHtml(value.substring(0, args) + '...');
        // } else {
        //   return this.sanitizer.bypassSecurityTrustHtml(value);
        // }
    }
};
EllipsisPipe = __decorate([
    Pipe({
        name: 'ellipsis'
    })
], EllipsisPipe);
export { EllipsisPipe };
