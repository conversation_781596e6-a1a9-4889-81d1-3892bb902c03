var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Component } from '@angular/core';
import { Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { STATE } from './services';
import * as jQuery from 'jquery';
// declare let $: any;
let AppComponent = class AppComponent {
    constructor(platformId, stateService, translate, cartService, cookieService) {
        this.platformId = platformId;
        this.stateService = stateService;
        this.translate = translate;
        this.cartService = cartService;
        this.cookieService = cookieService;
        this.title = 'angular-bootstrap';
        this.footerUrl = 'https://www.ganatan.com/';
        this.footerLink = 'www.ganatan.com';
        this.config = this.stateService.getState(STATE.CONFIG);
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        const { i18n, userLang } = this.config;
        if (userLang) {
            this.translate.setDefaultLang(userLang);
        }
        else if (i18n && i18n.defaultLanguage) {
            this.translate.setDefaultLang(i18n.defaultLanguage);
        }
    }
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            const navMain = document.getElementById('navbarCollapse');
            if (navMain) {
                navMain.onclick = function onClick() {
                    if (navMain) {
                        navMain.classList.remove('show');
                    }
                };
            }
            if (this.currentUser && this.currentUser._id) {
                const cartInfo = this.cookieService.get('cartInfo');
                if (cartInfo) {
                    this.cartService.updateCartInfoFromLocal(JSON.parse(cartInfo));
                }
            }
            const { colorScheme, siteFavicon } = this.config;
            (function ($) {
                if (siteFavicon) {
                    $('#favicon').attr('href', siteFavicon);
                }
                if (colorScheme && colorScheme.btn_color) {
                    $('head').append(`
          <style type="text/css">
             .nav-tabs .nav-link, .noti-link {
              color: ${colorScheme.btn_color};
              &:hover {
                color: ${colorScheme.btn_color}b3;
              }
            }
            .footer-link:hover {
                color: ${colorScheme.btn_color};
            }
            .btn.btn-default , .data-table .page-item.active .page-link, .btn.btn-footer, .btn.btn-light:hover, .wrapper-dashboard .left-sidebar .nav li a.active, .wrapper-dashboard .left-sidebar .nav li a:hover, .badge-default, .slot-btn {
              background-color: ${colorScheme.btn_color} !important;

              &:not(.no-hover)&:hover {
                background-color: ${colorScheme.btn_color}b3 !important;
              }
              &.no-hover&:hover {
                background-color: ${colorScheme.btn_color} !important;
              }
            }

            .bg-color-default {
              background-color: ${colorScheme.btn_color};
            }
            .header .nav-item .nav-link.active, .header .nav-item .nav-link:hover , .color-light-red , .wrapper-dashboard .left-sidebar .sidebar-nav-fixed .nav li:hover a, .wrapper-dashboard .sidebar-nav-fixed-mobile li:hover a, .wrapper-dashboard .left-sidebar .sidebar-nav-fixed .nav li.active a, {
              color: ${colorScheme.btn_color};
            }
            .pn-ProductNav_Link.active {
              color: ${colorScheme.btn_color};
            }
            .wrapper-dashboard .left-sidebar .main-sidebar .sidebar .pn-ProductNav_Link.active, .wrapper-dashboard .left-sidebar .main-sidebar .sidebar .pn-ProductNav_Link:hover, .morelink, .card-pagination.pagination li.active a,  .menu-item .img-card, .header .nav-item .nav-link.active, .header .nav-item .nav-link:hover  {
              color: ${colorScheme.btn_color} !important
            }
            input:checked + .slider, .custom-radio input:checked ~ .checkmark {
              background-color: ${colorScheme.btn_color};
            }
            .wrapper-dashboard .left-sidebar .sidebar-nav-fixed, .wrapper-dashboard .sidebar-nav-fixed-mobile, 
            .wrapper-dashboard .brand-link {
              border-bottom: 0.1rem solid ${colorScheme.btn_color};
            }
            .wizard li > a , .color-light-red {
              color: ${colorScheme.btn_color}
            }
            .wizard li.active, .wizard li:hover, .wizard .nav-tabs > li.active > a {
              background: ${colorScheme.btn_color};
              border-color: ${colorScheme.btn_color};
            }
            .wizard li:hover > a {
              color: #fff
            }
            .wizard .nav-tabs > li.active > a:hover, .wizard .nav-tabs > li.active > a:focus,  {
              background-color: ${colorScheme.btn_color};
            }
            .border-default-color, .border-solid {
              border-color: ${colorScheme.btn_color} !important;
            }
            .text-default-color, .color-primary, .hyperlink:hover {
              color: ${colorScheme.btn_color} !important;
            }
            .bg-secondary-default, .custom-radio .checkmark {
              background-color: ${colorScheme.btn_color}33;
            }
            .book_btnss_ a:hover {
              color:  ${colorScheme.btn_color};
            }
            .default-hover-color:hover {
              color:  ${colorScheme.btn_color} !important;
            }
            .nav-custom li.active a{
              color:  ${colorScheme.btn_color};
              border-color: ${colorScheme.btn_color};
            }
            .available-slot.active {
              background-color: ${colorScheme.btn_color} !important;
            }
            .slot-box{
              background-color: ${colorScheme.btn_color} !important; 
            }
          </style>`);
                }
                if (colorScheme && colorScheme.background_color) {
                    $('head').append(`
              <style type="text/css">
                .bg-color-light, .profile-page::before {
                  background-color: ${colorScheme.background_color}
                }
                .wrapper-dashboard .left-sidebar .main-sidebar .sidebar .pn-ProductNav_Link.active, .wrapper-dashboard .left-sidebar .main-sidebar .sidebar .pn-ProductNav_Link:hover, .btn.btn-light, .preloader {
                  background-color: ${colorScheme.background_color} !important
                }
                .available-slot {
                  background-color: ${colorScheme.background_color} !important;
                }
              </style>`);
                }
            })(jQuery);
        }
    }
};
AppComponent = __decorate([
    Component({
        selector: 'app-root',
        templateUrl: './app.component.html',
        styleUrls: ['./app.component.scss']
    }),
    __param(0, Inject(PLATFORM_ID))
], AppComponent);
export { AppComponent };
