var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ConversationsComponent } from './components/conversations/conversations.component';
import { MessagesComponent } from './components/messages/messages.component';
import { TranslateModule } from '@ngx-translate/core';
import { ConverstionsResolver } from 'src/app/services/resolvers';
import { SharedModule } from 'src/app/shared.module';
const routes = [
    {
        path: '',
        component: ConversationsComponent,
        resolve: {
            conversations: ConverstionsResolver
        }
    }
];
let MessageModule = class MessageModule {
};
MessageModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            RouterModule.forChild(routes),
            SharedModule,
            TranslateModule.forChild()
        ],
        declarations: [ConversationsComponent, MessagesComponent],
        exports: [ConversationsComponent, MessagesComponent]
    })
], MessageModule);
export { MessageModule };
