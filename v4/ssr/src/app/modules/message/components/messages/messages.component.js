var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Component, Input, ViewChild, ViewChildren, Output, EventEmitter, PLATFORM_ID, Inject } from '@angular/core';
import { STATE } from 'src/app/services';
import { isPlatformBrowser } from '@angular/common';
// import { SocketService } from '../../services/socket.service';
let MessagesComponent = class MessagesComponent {
    constructor(service, conversationService, toasty, translate, socket, stateService, platformId // private socket: Socket
    ) {
        this.service = service;
        this.conversationService = conversationService;
        this.toasty = toasty;
        this.translate = translate;
        this.socket = socket;
        this.stateService = stateService;
        this.platformId = platformId;
        this.items = [];
        this.page = 1;
        this.pageSize = 10;
        this.total = 0;
        this.currentUser = {};
        this.newText = '';
        this.receiver = null;
        this.loading = false;
        this.scrolltop = 0;
        this.doBack = new EventEmitter();
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        this.conversationSubscription =
            this.conversationService.conversationLoaded$.subscribe((data) => {
                if (this.conversation && this.conversation._id === data._id) {
                    return;
                }
                this.conversation = data;
                this.items = [];
                this.page = 1;
                this.query();
            });
    }
    ngOnInit() {
        if (this.conversation) {
            this.query();
        }
        if (isPlatformBrowser(this.platformId)) {
            this.socket.on('new_message', this.getMessage.bind(this));
        }
    }
    getMessage(msg) {
        if (this.conversation._id === msg.conversationId) {
            this.items = [...this.items, msg];
        }
    }
    ngOnDestroy() {
        // prevent memory leak when component destroyed
        this.conversationSubscription.unsubscribe();
        this.socket.off('new_message', this.getMessage.bind(this));
    }
    query() {
        this.loading = true;
        this.service
            .listByConversation(this.conversation._id, {
            page: this.page,
            take: this.pageSize
        })
            .then((resp) => {
            this.total = resp.data.count;
            this.items = resp.data.items.reverse().concat(this.items);
            this.loading = false;
            const receivers = (this.conversation.members || []).filter((m) => m._id !== this.currentUser._id);
            this.receiver = receivers && receivers.length ? receivers[0] : null;
            // this.scrolltop = this.comment.nativeElement.scrollHeight;
            this.itemElements.changes.subscribe(() => this.onItemElementsChanged());
        });
    }
    onItemElementsChanged() {
        this.scrollToBottom();
    }
    scrollToBottom() {
        this.comment.nativeElement.scroll({
            top: this.comment.nativeElement.scrollHeight,
            left: 0,
            behavior: 'smooth'
        });
    }
    loadMore() {
        this.loading = true;
        this.page++;
        this.service
            .listByConversation(this.conversation._id, {
            page: this.page,
            take: this.pageSize
        })
            .then((resp) => {
            this.total = resp.data.count;
            this.items = resp.data.items.reverse().concat(this.items);
            $('.msg_history').animate({ scrollTop: 0 }, 500);
            this.loading = false;
        });
    }
    send() {
        if (!this.newText) {
            return this.toasty.error(this.translate.instant('Please enter message'));
        }
        this.service
            .send({
            text: this.newText,
            type: 'text',
            conversationId: this.conversation._id
        })
            .then((resp) => {
            this.items.push(resp.data);
            this.service.afterSendSuccess(this.conversation._id, resp.data);
            this.newText = '';
            this.scrolltop = this.comment.nativeElement.scrollHeight;
        });
    }
    enterToSend(event) {
        if (event.charCode === 13) {
            this.send();
        }
    }
    goBack() {
        this.doBack.emit(true);
    }
};
__decorate([
    Input()
], MessagesComponent.prototype, "conversation", void 0);
__decorate([
    ViewChild('commentEl')
], MessagesComponent.prototype, "comment", void 0);
__decorate([
    ViewChildren('item')
], MessagesComponent.prototype, "itemElements", void 0);
__decorate([
    Output()
], MessagesComponent.prototype, "doBack", void 0);
MessagesComponent = __decorate([
    Component({
        selector: 'app-messages',
        templateUrl: './messages.html'
    }),
    __param(6, Inject(PLATFORM_ID))
], MessagesComponent);
export { MessagesComponent };
