var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
// import { PusherService } from '../../services/pusher.service';
import { orderBy } from 'lodash';
import { STATE } from 'src/app/services';
let ConversationsComponent = class ConversationsComponent {
    constructor(route, service, socket, seoService, messageService, stateService) {
        this.route = route;
        this.service = service;
        this.socket = socket;
        this.seoService = seoService;
        this.messageService = messageService;
        this.stateService = stateService;
        this.originalConversations = [];
        this.conversations = [];
        this.q = '';
        this.show = false;
        this.seoService.setMetaTitle('Messages');
        // this.socket.connect();
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        this.conversations = this.mapConversationName(route.snapshot.data.conversations);
        this.originalConversations = this.conversations;
        this.socket.getMessage(this.getMessage.bind(this));
        this.sendMessageSubscription = this.messageService.sendMessage$.subscribe(data => {
            const { conversationId, message } = data;
            if (conversationId && message) {
                const conversation = this.conversations.find((item) => item._id.toString() === conversationId.toString());
                if (conversation) {
                    conversation.lastMessage = message;
                    conversation.updatedAt = message.createdAt;
                    this.conversations = orderBy(this.conversations, ['updatedAt'], ['desc']);
                }
            }
        });
    }
    getMessage(msg) {
        if (this.activeConversation && this.activeConversation._id === msg.conversationId) {
            this.activeConversation.lastMessage = msg;
            this.activeConversation.updatedAt = msg.createdAt;
            this.conversations = orderBy(this.conversations, ['updatedAt'], ['desc']);
            return;
        }
        else {
            const conversation = this.conversations.find((item) => item._id.toString() === msg.conversationId);
            if (conversation) {
                conversation.userMeta.unreadMessage += 1;
                conversation.lastMessage = msg;
                conversation.updatedAt = msg.createdAt;
                this.conversations = orderBy(this.conversations, ['updatedAt'], ['desc']);
            }
            else {
                this.service.findOne(msg.conversationId).then(resp => {
                    if (resp.data) {
                        resp.data.lastMessage = msg;
                        const newConversation = this.mapConversationName([resp.data]);
                        this.conversations = newConversation.concat(this.conversations);
                        this.conversations = orderBy(this.conversations, ['updatedAt'], ['desc']);
                    }
                });
            }
        }
    }
    mapConversationName(conversations = []) {
        return conversations.map((conversation) => {
            const member = (conversation.members || []).filter((m) => m._id && m._id !== this.currentUser._id);
            conversation.name = member.length ? member[0].name : this.currentUser.name;
            conversation.member = member.length ? member[0] : this.currentUser;
            return conversation;
        });
    }
    selectConversation(conversation) {
        this.activeConversation = conversation;
        this.service.setActive(conversation);
        this.show = true;
        if (conversation && conversation.userMeta && conversation.userMeta.unreadMessage > 0) {
            this.service
                .read(conversation._id, { all: true })
                .then(resp => {
                if (resp && resp.data && resp.data.success) {
                    conversation.userMeta.unreadMessage = 0;
                }
            })
                .catch(err => console.log(err));
        }
    }
    filter() {
        this.conversations = this.originalConversations.filter((conversation) => conversation.name.toLowerCase().indexOf(this.q) > -1);
    }
    enterToSend(event) {
        if (event.charCode === 13) {
            this.filter();
        }
    }
    substringMessage(text) {
        if (text && text.length > 55) {
            return text.substring(0, 55) + '...';
        }
        return text;
    }
    ngOnDestroy() {
        // this.socket.disconnect();
        // return;
        this.socket.off('new_message', this.getMessage.bind(this));
    }
    onBack() {
        this.show = false;
    }
};
ConversationsComponent = __decorate([
    Component({
        templateUrl: './conversations.html'
    })
], ConversationsComponent);
export { ConversationsComponent };
