var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { ageFilter, randomHash, quillConfig } from 'src/app/lib';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
let WebinarCreateComponent = class WebinarCreateComponent {
    constructor(router, webinarService, appService, myCategoryService, calendarService, gradeService, mySubjectService, myTopicService, mediaService, stateService) {
        this.router = router;
        this.webinarService = webinarService;
        this.appService = appService;
        this.myCategoryService = myCategoryService;
        this.calendarService = calendarService;
        this.gradeService = gradeService;
        this.mySubjectService = mySubjectService;
        this.myTopicService = myTopicService;
        this.mediaService = mediaService;
        this.stateService = stateService;
        this.webinar = {
            name: '',
            maximumStrength: 1,
            categoryIds: [],
            isOpen: false,
            price: 0,
            mediaIds: [],
            mainImageId: '',
            description: '',
            alias: '',
            isFree: false,
            gradeIds: [],
            subjectIds: [],
            topicIds: []
        };
        this.isSubmitted = false;
        this.medias = [];
        this.mainImageUrl = '';
        this.imageSelected = [];
        this.filesSelected = [];
        this.loading = false;
        this.quillConfig = quillConfig;
        this.grades = [];
        this.myCategories = [];
        this.mySubjects = [];
        this.myTopics = [];
        this.ageFilter = ageFilter;
        this.tab = 'basicInfo';
        this.maxFileSize = environment.maximumFileSize;
        this.config = this.stateService.getState(STATE.CONFIG);
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
    }
    ngOnInit() {
        this.mainImageOptions = {
            url: environment.apiBaseUrl + '/media/photos',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.webinar.mainImageId = resp.data._id;
                this.mainImageUrl = resp.data.thumbUrl;
            },
            onFileSelect: (resp) => (this.imageSelected = resp),
            accept: 'image/*',
            id: 'image-upload'
        };
        this.mediaOptions = {
            url: environment.apiBaseUrl + '/media/files',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.webinar.mediaIds.push(resp.data._id);
                this.medias.push(resp.data);
            },
            onFileSelect: (resp) => (this.filesSelected = resp),
            id: 'file-upload'
        };
        this.queryMyCategories();
        this.queryGrades();
        this.hashWebinar = localStorage.getItem('hast_webinar');
        if (!this.hashWebinar) {
            this.hashWebinar = randomHash(32, '');
            localStorage.setItem('hast_webinar', this.hashWebinar);
        }
    }
    ngOnDestroy() {
        if (this.hashWebinar) {
            this.calendarService.deleteByHash(this.hashWebinar);
            localStorage.removeItem('hast_webinar');
        }
    }
    queryMyCategories() {
        this.loading = true;
        this.myCategoryService
            .getListOfMe({
            take: 100,
            sort: 'ordering',
            sortType: 'asc',
            isActive: true
        })
            .then((resp) => {
            this.myCategories = resp.data.items;
            this.loading = false;
        })
            .catch((err) => {
            this.loading = false;
            this.appService.toastError(err);
        });
    }
    queryGrades() {
        this.loading = true;
        this.gradeService
            .search({ take: 100, sort: 'ordering', sortType: 'asc' })
            .then((resp) => {
            this.grades = resp.data.items;
            this.loading = false;
        })
            .catch((err) => {
            this.loading = false;
            this.appService.toastError(err);
        });
    }
    removeMedia(media, i) {
        this.mediaService
            .remove(media._id)
            .then((resp) => {
            if (resp.data && resp.data.ok) {
                this.appService.toastSuccess('Removed media!');
                this.webinar.mediaIds.splice(i, 1);
                this.medias.splice(i, 1);
            }
        })
            .catch((err) => this.appService.toastError(err));
    }
    submit(frm) {
        this.isSubmitted = true;
        if (!frm.valid) {
            return this.appService.toastError('Invalid form, please try again.');
        }
        if (this.webinar.price <= 0 && !this.webinar.isFree) {
            return this.appService.toastError('Price value should be greater than 0');
        }
        if (this.webinar.maximumStrength > 10 &&
            this.config?.platformOnline === 'lessonspace') {
            return this.appService.toastError('Lesson space allows only 10 users for group class!');
        }
        if (!this.webinar.mainImageId)
            return this.appService.toastError('Please upload main image for webinar!');
        if (this.webinar.isFree === true)
            this.webinar.price = 0;
        if (this.webinar.description) {
            this.webinar.description = this.webinar.description.replace('<p data-f-id="pbf" style="text-align: center; font-size: 14px; margin-top: 30px; opacity: 0.65; font-family: sans-serif;">Powered by <a href="https://www.froala.com/wysiwyg-editor?pb=1" title="Froala Editor">Froala Editor</a></p>', '');
        }
        this.calendarService.checkByHash(this.hashWebinar).then((check) => {
            if (!check.data.success) {
                return this.appService.toastError('Please create schedule for group class if you want the group class to be public');
            }
            const data = this.hashWebinar
                ? Object.assign(this.webinar, { hashWebinar: this.hashWebinar })
                : this.webinar;
            this.webinarService.create(data).then(() => {
                localStorage.removeItem('hast_webinar');
                this.appService.toastSuccess('Group Class created successfully!');
                this.router.navigate(['/users/groupclass']);
            }, (err) => this.appService.toastError(err));
        });
    }
    onSelectMyCategories(items) {
        if (items?.length) {
            this.queryMySubjects(items.map((item) => item._id).join(','));
        }
        else {
            this.mySubjects = [];
            this.myTopics = [];
            this.webinar.subjectIds = [];
            this.webinar.topicIds = [];
        }
    }
    queryMySubjects(myCategoryIds) {
        this.mySubjectService
            .getListOfMe({
            take: 100,
            sort: 'ordering',
            sortType: 'asc',
            myCategoryIds,
            isActive: true
        })
            .then((resp) => {
            if (resp.data && resp.data.items) {
                this.mySubjects = resp.data.items;
                const mySubjectSelected = this.mySubjects.filter((item) => this.webinar.subjectIds.includes(item._id));
                this.webinar.subjectIds = mySubjectSelected.map((item) => item.originalSubjectId);
                this.queryMyTopics(mySubjectSelected.map((item) => item._id).join(','));
            }
        })
            .catch((err) => {
            return this.appService.toastError(err);
        });
    }
    onSelectMySubjects(items) {
        if (items?.length) {
            this.queryMyTopics(items.map((item) => item._id).join(','));
        }
        else {
            this.myTopics = [];
            this.webinar.topicIds = [];
        }
    }
    queryMyTopics(mySubjectIds) {
        if (!mySubjectIds) {
            this.myTopics = [];
            this.webinar.topicIds = [];
        }
        else
            this.myTopicService
                .getListOfMe({
                take: 100,
                sort: 'ordering',
                sortType: 'asc',
                mySubjectIds,
                isActive: true
            })
                .then((resp) => {
                if (resp.data && resp.data.items) {
                    this.myTopics = resp.data.items;
                    const myTopicSelected = this.myTopics.filter((item) => this.webinar.topicIds.includes(item._id));
                    this.webinar.topicIds = myTopicSelected.map((item) => item.originalTopicId);
                }
            })
                .catch((err) => {
                return this.appService.toastError(err);
            });
    }
    onTabSelect(tab = '') {
        return tab;
    }
};
WebinarCreateComponent = __decorate([
    Component({ selector: 'app-webinar-create', templateUrl: '../form.html' })
], WebinarCreateComponent);
export { WebinarCreateComponent };
