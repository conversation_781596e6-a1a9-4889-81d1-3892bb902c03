var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { ParticipantFormComponent } from '../modal-participants/participants-form';
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let WebinarListingComponent = class WebinarListingComponent {
    constructor(webinarService, appService, auth, seoService, modalService, stateService) {
        this.webinarService = webinarService;
        this.appService = appService;
        this.auth = auth;
        this.seoService = seoService;
        this.modalService = modalService;
        this.stateService = stateService;
        this.total = 0;
        this.currentPage = 1;
        this.pageSize = 10;
        this.searchFields = {};
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.fromItem = 0;
        this.toItem = 0;
        this.updating = false;
        this.seoService.setMetaTitle('My Group Classes');
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.auth.getCurrentUser().then((resp) => {
            this.currentUser = resp;
            if (this.currentUser._id) {
                this.query();
            }
        });
    }
    query() {
        this.webinarService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            tutorId: this.currentUser._id,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            ...this.searchFields
        })
            .then((resp) => {
            this.total = resp.data.count;
            this.items = resp.data.items;
            if (this.currentPage === 1) {
                this.fromItem = this.currentPage;
                this.toItem = this.items.length;
            }
            else if (this.currentPage > 1) {
                this.fromItem =
                    this.currentPage * this.pageSize > this.total
                        ? (this.currentPage - 1) * this.pageSize
                        : this.currentPage * this.pageSize;
                this.toItem = this.fromItem + this.items.length;
            }
        })
            .catch(() => this.appService.toastError());
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
    showChange(evt) {
        this.pageSize = evt.target.value;
        this.query();
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    remove(item, index) {
        if (window.confirm('Are you sure to delete this webinar?')) {
            this.webinarService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.items.splice(index, 1);
            })
                .catch((e) => this.appService.toastError(e));
        }
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    showParticipants(webinar) {
        const modalRef = this.modalService.open(ParticipantFormComponent, {
            centered: true,
            backdrop: 'static',
            size: 'lg'
        });
        modalRef.componentInstance.webinarId = webinar._id;
    }
    changeStatus(webinar) {
        if (!this.updating) {
            this.updating = true;
            this.webinarService
                .changeStatus(webinar._id)
                .then(() => {
                webinar['disabled'] = !webinar.disabled;
                const message = webinar.disabled ? 'Disabled' : 'Enabled';
                this.appService.toastSuccess(message);
                this.updating = false;
            })
                .catch((err) => {
                this.updating = false;
                return this.appService.toastError(err);
            });
        }
    }
};
WebinarListingComponent = __decorate([
    Component({
        selector: 'app-webinar-listing',
        templateUrl: './list.html'
    })
], WebinarListingComponent);
export { WebinarListingComponent };
