var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import chunk from 'lodash/chunk';
let ParticipantFormComponent = class ParticipantFormComponent {
    constructor(webinarService, activeModal) {
        this.webinarService = webinarService;
        this.activeModal = activeModal;
        this.participants = [];
        this.loading = false;
        this.chunks = [];
    }
    ngOnInit() {
        this.loading = true;
        this.webinarService.getEnrolledList(this.webinarId).then((resp) => {
            if (resp.data && resp.data.items && resp.data.items.length) {
                this.participants = resp.data.items.map((item) => item.user);
            }
            if (this.participants.length > 11) {
                this.chunks = chunk(this.participants, 11);
            }
            this.loading = false;
        });
    }
};
__decorate([
    Input()
], ParticipantFormComponent.prototype, "webinarId", void 0);
ParticipantFormComponent = __decorate([
    Component({
        selector: 'app-participants-form',
        templateUrl: './participants-form.html'
    })
], ParticipantFormComponent);
export { ParticipantFormComponent };
