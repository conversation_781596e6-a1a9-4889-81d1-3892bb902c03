var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let FavoriteComponent = class FavoriteComponent {
    constructor(route, seoService, favoriteService, appService) {
        this.route = route;
        this.seoService = seoService;
        this.favoriteService = favoriteService;
        this.appService = appService;
        this.page = 1;
        this.pageSize = 9;
        this.items = {
            tutor: [],
            webinar: [],
            course: []
        };
        this.total = 0;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = false;
        this.haveResults = false;
        this.seoService.setMetaTitle('My favorite');
        this.route.params.subscribe((params) => {
            this.type = params.type === 'groupclass' ? 'webinar' : params.type;
            this.query();
        });
    }
    reset() {
        this.page = 1;
        this.items.tutor = [];
        this.items.webinar = [];
        this.items.course = [];
        this.loading = false;
    }
    query() {
        this.reset();
        const params = Object.assign({
            page: this.page,
            take: this.pageSize,
            sort: this.sortOption.sortBy,
            sortType: this.sortOption.sortType
        });
        if (!this.loading) {
            if (this.type) {
                this.loading = true;
                this.favoriteService
                    .search(params, this.type)
                    .then((resp) => {
                    if (resp &&
                        resp.data &&
                        resp.data.items &&
                        resp.data.items.length) {
                        this.items[this.type] = resp.data.items;
                        this.total = resp.data.count;
                    }
                    this.loading = false;
                })
                    .catch(() => {
                    this.loading = false;
                    this.appService.toastError();
                });
            }
        }
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
};
FavoriteComponent = __decorate([
    Component({
        templateUrl: 'favorite.html'
    })
], FavoriteComponent);
export { FavoriteComponent };
