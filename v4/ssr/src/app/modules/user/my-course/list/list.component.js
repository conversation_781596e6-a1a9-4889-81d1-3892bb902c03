var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let ListMyCourseComponent = class ListMyCourseComponent {
    constructor(auth, myCourseService, route, seoService, appService) {
        this.auth = auth;
        this.myCourseService = myCourseService;
        this.route = route;
        this.seoService = seoService;
        this.appService = appService;
        this.currentPage = 1;
        this.pageSize = 10;
        this.total = 2;
        this.categories = [];
        this.searchFields = {
            categoryIds: ''
        };
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.courses = [];
        this.count = 0;
        this.loading = false;
        this.seoService.setMetaTitle('My Courses');
    }
    ngOnInit() {
        this.categories = this.route.snapshot.data['categories'];
        if (this.auth.isLoggedin()) {
            this.auth.getCurrentUser().then((resp) => {
                this.currentUser = resp;
                this.query();
            });
        }
    }
    query() {
        this.loading = true;
        const params = Object.assign({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            userId: this.currentUser._id
        }, this.searchFields);
        this.myCourseService
            .search(params)
            .then((resp) => {
            this.count = resp.data.count;
            this.courses = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
};
ListMyCourseComponent = __decorate([
    Component({
        templateUrl: './list.html'
    })
], ListMyCourseComponent);
export { ListMyCourseComponent };
