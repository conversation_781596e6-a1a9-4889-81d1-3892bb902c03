var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { environment } from 'src/environments/environment';
import { encrypt } from 'src/app/lib';
import { pick } from 'lodash';
let ModalAppointmentComponent = class ModalAppointmentComponent {
    constructor(activeModal, appointmentService, appService, router) {
        this.activeModal = activeModal;
        this.appointmentService = appointmentService;
        this.appService = appService;
        this.router = router;
        this.appointments = [];
        this.currentPage = 1;
        this.pageSize = 5;
        this.searchFields = {};
        this.sortOption = {
            sortBy: 'startTime',
            sortType: 'asc'
        };
        this.loading = false;
        this.joining = false;
        this.starting = false;
    }
    ngOnInit() {
        if (this.webinar) {
            if (this.type === 'student') {
                this.queryAppointmentStudent();
            }
            else {
                this.queryAppointmentTutor();
            }
        }
    }
    async queryAppointmentStudent() {
        this.loading = true;
        await this.appointmentService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            userId: this.currentUser._id,
            targetType: 'webinar',
            webinarId: this.webinar._id,
            ...this.searchFields
        })
            .then((resp) => {
            this.appointments = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    async queryAppointmentTutor() {
        this.loading = true;
        await this.appointmentService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            tutorId: this.currentUser._id,
            targetType: 'webinar',
            webinarId: this.webinar._id,
            userId: this.studentId || '',
            ...this.searchFields
        })
            .then((resp) => {
            this.appointments = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    pageChange() {
        if (this.type === 'student') {
            this.queryAppointmentStudent();
        }
        else {
            this.queryAppointmentTutor();
        }
    }
    joinMeeting(appointmentId) {
        if (!this.joining) {
            this.joining = true;
            this.appointmentService
                .joinMeeting(appointmentId)
                .then((resp) => {
                this.joining = false;
                if (resp.data &&
                    resp.data.platform === 'zoomus' &&
                    resp.data['zoomus'].signature) {
                    const token = encrypt({
                        meetingInfo: resp.data['zoomus'],
                        appointmentId: appointmentId,
                        currentUser: pick(this.currentUser, ['name', 'email', 'type'])
                    }, '');
                    window.location.href = `${environment.zoomSiteUrl}?token=${encodeURIComponent(token)}`;
                }
                else if (resp.data &&
                    resp.data.platform === 'lessonspace' &&
                    resp.data['lessonspace'].url) {
                    localStorage.setItem('lessonSpaceUrl', resp.data['lessonspace'].url);
                    this.router.navigate(['/users/lesson-space'], {
                        queryParams: {
                            appointmentId
                        }
                    });
                }
                this.close();
            })
                .catch((err) => {
                this.joining = false;
                return this.appService.toastError(err);
            });
        }
        else {
            this.appService.toastSuccess('Connecting...');
        }
    }
    startMeeting(appointmentId) {
        if (!this.starting) {
            this.starting = true;
            this.appointmentService
                .startMeeting(appointmentId)
                .then((resp) => {
                this.starting = false;
                if (resp.data &&
                    resp.data.platform === 'zoomus' &&
                    resp.data['zoomus'].signature) {
                    const token = encrypt({
                        meetingInfo: resp.data['zoomus'],
                        appointmentId: appointmentId,
                        currentUser: pick(this.currentUser, ['name', 'email', 'type'])
                    }, '');
                    window.location.href = `${environment.zoomSiteUrl}?token=${encodeURIComponent(token)}`;
                }
                else if (resp.data &&
                    resp.data.platform === 'lessonspace' &&
                    resp.data['lessonspace'].url) {
                    localStorage.setItem('lessonSpaceUrl', resp.data['lessonspace'].url);
                    this.router.navigate(['/users/lesson-space'], {
                        queryParams: {
                            appointmentId
                        }
                    });
                }
                this.close();
            })
                .catch((err) => {
                this.starting = false;
                return this.appService.toastError(err);
            });
        }
        else {
            this.appService.toastSuccess('Connecting...');
        }
    }
    close() {
        this.activeModal.close(true);
    }
};
__decorate([
    Input()
], ModalAppointmentComponent.prototype, "appointments", void 0);
__decorate([
    Input()
], ModalAppointmentComponent.prototype, "currentUser", void 0);
__decorate([
    Input()
], ModalAppointmentComponent.prototype, "webinar", void 0);
__decorate([
    Input()
], ModalAppointmentComponent.prototype, "type", void 0);
__decorate([
    Input()
], ModalAppointmentComponent.prototype, "transactionId", void 0);
ModalAppointmentComponent = __decorate([
    Component({
        selector: 'app-modal-appointment',
        templateUrl: './modal.html'
    })
], ModalAppointmentComponent);
export { ModalAppointmentComponent };
