var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { pick } from 'lodash';
import { environment } from 'src/environments/environment';
let SectionFormComponent = class SectionFormComponent {
    constructor(toasty, activeModal, translate) {
        this.toasty = toasty;
        this.activeModal = activeModal;
        this.translate = translate;
        this.section = {};
        this.submitted = false;
        this.uploading = false;
        this.videoSelected = [];
        this.videoUrl = '';
    }
    ngOnInit() {
        this.videoUrl = this.section.trialVideo ? this.section.trialVideo.fileUrl : null;
        this.videoOptions = {
            url: environment.apiBaseUrl + '/media/videos',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.uploading = false;
                this.section.trialVideoId = resp.data._id;
                this.videoUrl = resp.data.fileUrl;
            },
            id: 'section-video-trial',
            accept: 'video/*',
            onUploading: () => (this.uploading = true),
            onFileSelect: (resp) => (this.videoSelected = resp)
        };
    }
    submit(frm) {
        this.submitted = true;
        if (!frm.valid || this.section.ordering < 0) {
            return this.toasty.error(this.translate.instant('Please complete the required fields!'));
        }
        this.activeModal.close(pick(this.section, ['title', 'description', 'ordering', 'trialVideoId']));
    }
};
__decorate([
    Input()
], SectionFormComponent.prototype, "section", void 0);
SectionFormComponent = __decorate([
    Component({
        selector: 'app-section-form',
        templateUrl: './section-form.html'
    })
], SectionFormComponent);
export { SectionFormComponent };
