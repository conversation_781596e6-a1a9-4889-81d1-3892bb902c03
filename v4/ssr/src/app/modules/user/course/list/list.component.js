var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let CourseListingComponent = class CourseListingComponent {
    constructor(translate, courseService, appService, auth, route, seoService, stateService) {
        this.translate = translate;
        this.courseService = courseService;
        this.appService = appService;
        this.auth = auth;
        this.route = route;
        this.seoService = seoService;
        this.stateService = stateService;
        this.total = 0;
        this.currentPage = 1;
        this.pageSize = 10;
        this.categories = [];
        this.searchFields = {
            categoryIds: ''
        };
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.fromItem = 0;
        this.toItem = 0;
        this.loading = false;
        this.seoService.setMetaTitle('Courses Manager');
    }
    ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
        this.categories = this.route.snapshot.data['categories'];
        this.auth.getCurrentUser().then((resp) => {
            this.currentUser = resp;
            if (this.currentUser._id) {
                this.query();
            }
        });
    }
    query() {
        this.loading = true;
        this.courseService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            tutorId: this.currentUser._id,
            ...this.searchFields
        })
            .then((resp) => {
            this.total = resp.data.count;
            this.items = resp.data.items;
            if (this.currentPage === 1) {
                this.fromItem = this.currentPage;
                this.toItem = this.items.length;
            }
            else if (this.currentPage > 1) {
                this.fromItem =
                    this.currentPage * this.pageSize > this.total
                        ? (this.currentPage - 1) * this.pageSize
                        : this.currentPage * this.pageSize;
                this.toItem = this.fromItem + this.items.length;
            }
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
    showChange(evt) {
        this.pageSize = evt.target.value;
        this.query();
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    remove(item, index) {
        if (window.confirm(this.translate.instant('Are you sure to delete this course?'))) {
            this.courseService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Course has been deleted!');
                this.items.splice(index, 1);
            })
                .catch((e) => this.appService.toastError(e));
        }
    }
};
CourseListingComponent = __decorate([
    Component({
        selector: 'app-course-listing',
        templateUrl: './list.html'
    })
], CourseListingComponent);
export { CourseListingComponent };
