var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input, Output, EventEmitter } from '@angular/core';
import pick from 'lodash/pick';
let CourseGoalComponent = class CourseGoalComponent {
    constructor(translate, appService, courseService) {
        this.translate = translate;
        this.appService = appService;
        this.courseService = courseService;
        this.doTabSelect = new EventEmitter();
        this.courseGoal = {
            goalCourse: '',
            whyJoinCourse: '',
            needToJoinCourse: ''
        };
        this.isSubmitted = false;
        this.index = 0;
    }
    ngOnInit() {
        this.courseId = this.course._id;
    }
    addItem(type) {
        this.course[type].push(this.courseGoal[type]);
        this.courseGoal[type] = '';
        this.submit();
    }
    submit() {
        this.isSubmitted = true;
        if (this.course._id) {
            this.courseService
                .update(this.courseId, pick(this.course, [
                'name',
                'price',
                'description',
                'alias',
                'categoryIds',
                'introductionVideoId',
                'mainImageId',
                'isFree',
                'goalCourse',
                'whyJoinCourse',
                'needToJoinCourse'
            ]))
                .then(() => {
                this.appService.toastSuccess('Updated successfuly!');
                //this.router.navigate(['/users/courses']);
            })
                .catch((err) => {
                this.appService.toastError(err);
            });
        }
    }
    removeItem(type, i) {
        if (window.confirm(this.translate.instant('Are you sure to delete this?'))) {
            this.course[type].splice(i, 1);
            this.submit();
        }
    }
    onTab(tab) {
        this.doTabSelect.emit(tab);
    }
};
__decorate([
    Input()
], CourseGoalComponent.prototype, "course", void 0);
__decorate([
    Output()
], CourseGoalComponent.prototype, "doTabSelect", void 0);
CourseGoalComponent = __decorate([
    Component({
        selector: 'app-course-goal',
        templateUrl: './course-goal.html'
    })
], CourseGoalComponent);
export { CourseGoalComponent };
