var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { pick } from 'lodash';
import { environment } from 'src/environments/environment';
import { randomHash } from 'src/app/lib';
let LectureFormComponent = class LectureFormComponent {
    constructor(appService, lectureMediaService, activeModal) {
        this.appService = appService;
        this.lectureMediaService = lectureMediaService;
        this.activeModal = activeModal;
        this.lecture = { mediaIds: [] };
        this.submitted = false;
        this.videoOptions = {};
        this.pdfOptions = {};
        this.audioOptions = {};
        this.mediaType = 'video';
        this.mediaOptions = {};
        this.uploading = false;
        this.lectureMedia = {
            mediaType: 'video'
        };
        this.hashLecture = '';
        this.medias = [];
        this.maxFileSize = environment.maximumFileSize;
    }
    ngOnInit() {
        // if (!this.lecture._id) this.lecture.mediaType = 'video';
        if (this.lecture._id)
            this.lectureMediaService
                .search({ lectureId: this.lecture._id })
                .then((resp) => {
                this.medias = resp.data.items;
            });
        else {
            this.hashLecture = randomHash(32, '');
        }
        this.videoOptions = {
            url: environment.apiBaseUrl + '/media/videos',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.lectureMediaService
                    .create({
                    lectureId: this.lecture._id || null,
                    hashLecture: this.hashLecture,
                    mediaType: 'video',
                    mediaId: resp.data._id
                })
                    .then((res) => {
                    this.medias.push(res.data);
                    if (this.lecture._id)
                        this.lecture?.mediaIds.push(res.data._id);
                    this.uploading = false;
                });
            },
            id: 'video-upload',
            accept: 'video/*',
            onUploading: () => (this.uploading = true)
        };
        this.audioOptions = {
            url: environment.apiBaseUrl + '/media/audios',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.lectureMediaService
                    .create({
                    lectureId: this.lecture._id || null,
                    hashLecture: this.hashLecture,
                    mediaType: 'audio',
                    mediaId: resp.data._id
                })
                    .then((res) => {
                    this.medias.push(res.data);
                    if (this.lecture._id)
                        this.lecture.mediaIds.push(res.data._id);
                    this.uploading = false;
                });
            },
            id: 'audio-upload',
            accept: 'audio/*',
            onUploading: () => (this.uploading = true)
        };
        this.pdfOptions = {
            url: environment.apiBaseUrl + '/media/files',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.lectureMediaService
                    .create({
                    lectureId: this.lecture._id || null,
                    hashLecture: this.hashLecture,
                    mediaType: 'pdf',
                    mediaId: resp.data._id,
                    totalLength: this.lectureMedia.totalLength
                })
                    .then((res) => {
                    this.medias.push(res.data);
                    if (this.lecture._id)
                        this.lecture.mediaIds.push(res.data._id);
                    this.uploading = false;
                    this.lectureMedia.totalLength = 0;
                });
            },
            lecturePdf: true,
            id: 'pdf-upload',
            accept: '.pdf',
            onUploading: () => (this.uploading = true)
        };
        // if (this.lecture.mediaType) this.mediaType = this.lecture.mediaType;
        this.lecture.courseId = this.courseId;
    }
    onUpload(event) {
        this.lectureMedia.totalLength = event;
    }
    submit(frm) {
        if (!frm.valid) {
            return this.appService.toastError('Invalid form, please try again.');
        }
        this.submitted = true;
        if (!this.medias.length)
            this.appService.toastError('Please upload media content for lecture!');
        else
            this.activeModal.close(Object.assign(pick(this.lecture, [
                'courseId',
                'sectionId',
                'title',
                'description',
                'ordering',
                'mediaIds'
            ]), {
                hashLecture: this.hashLecture
            }));
    }
    checkMediaType(type) {
        this.lectureMedia.mediaType = type;
        this.mediaType = type;
    }
    returnDuration(seconds) {
        if (seconds == 0)
            return 'converting';
        else if (seconds < 10)
            return '00:0' + seconds;
        let duration = '';
        if (seconds < 60)
            return '00:' + seconds;
        else {
            let hour, second;
            hour = seconds < 3600 ? 0 : Math.floor(seconds / 3600);
            if (hour > 0) {
                if (hour < 10)
                    hour = '0' + hour;
                duration = hour + ':';
            }
            const remainSecond = seconds - parseInt(hour) * 3600;
            const minute = Math.floor(remainSecond / 60) < 10
                ? '0' + Math.floor(remainSecond / 60)
                : Math.floor(remainSecond / 60);
            second =
                seconds - parseInt(hour) * 3600 - minute * 60;
            if (second < 10)
                second = '0' + second;
            return duration + minute + ':' + second;
        }
    }
    removeMedia(item, index) {
        if (window.confirm('Are you sure to delete this media content?')) {
            this.lectureMediaService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.medias.splice(index, 1);
            })
                .catch((e) => this.appService.toastError(e));
        }
    }
};
__decorate([
    Input()
], LectureFormComponent.prototype, "lecture", void 0);
__decorate([
    Input()
], LectureFormComponent.prototype, "courseId", void 0);
LectureFormComponent = __decorate([
    Component({
        selector: 'app-lecture-form',
        templateUrl: './lecture-form.html'
    })
], LectureFormComponent);
export { LectureFormComponent };
