var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input, Output, EventEmitter } from '@angular/core';
let CourseCouponComponent = class CourseCouponComponent {
    constructor() {
        this.doTabSelect = new EventEmitter();
    }
    ngOnInit() { }
    onTab(tab) {
        this.doTabSelect.emit(tab);
    }
};
__decorate([
    Input()
], CourseCouponComponent.prototype, "course", void 0);
__decorate([
    Input()
], CourseCouponComponent.prototype, "tutorId", void 0);
__decorate([
    Output()
], CourseCouponComponent.prototype, "doTabSelect", void 0);
CourseCouponComponent = __decorate([
    Component({
        selector: 'app-course-coupon',
        templateUrl: './course-coupon.html'
    })
], CourseCouponComponent);
export { CourseCouponComponent };
