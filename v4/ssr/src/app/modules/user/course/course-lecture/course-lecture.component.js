var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { sortBy } from 'lodash';
import { SectionFormComponent } from '../modal-section/section-form';
import { LectureFormComponent } from '../modal-lecture/lecture-form';
import * as jQuery from 'jquery';
let CourseLetureComponent = class CourseLetureComponent {
    constructor(appService, sectionService, modalService, lectureService) {
        this.appService = appService;
        this.sectionService = sectionService;
        this.modalService = modalService;
        this.lectureService = lectureService;
        this.doTabSelect = new EventEmitter();
        this.isSubmitted = false;
        this.sections = [];
        this.lectures = [];
        this.loading = false;
    }
    ngOnInit() {
        this.courseId = this.course._id;
        if (this.courseId) {
            this.loading = true;
            this.sectionService
                .search({
                courseId: this.courseId,
                take: 100,
                sort: 'ordering',
                sortType: 'asc'
            })
                .then((resp) => {
                if (resp.data) {
                    this.sections = resp.data.items;
                    this.sections.map((section) => {
                        section.lectures = sortBy(section.lectures, ['ordering']);
                    });
                    this.loading = false;
                    (function ($) {
                        $(document).ready(function () {
                            $('#accordion').on('hide.bs.collapse show.bs.collapse', (e) => {
                                $(e.target)
                                    .prev()
                                    .find('.btn-collapse i:last-child')
                                    .toggleClass('fa-minus fa-plus');
                            });
                        });
                    })(jQuery);
                }
            })
                .catch((err) => {
                this.appService.toastError(err);
                this.loading = false;
            });
        }
    }
    onTab(tab) {
        this.doTabSelect.emit(tab);
    }
    submitSection(section = {}) {
        const modalRef = this.modalService.open(SectionFormComponent, {
            centered: true,
            backdrop: 'static',
            size: 'lg'
        });
        modalRef.componentInstance.section = section;
        modalRef.result.then((res) => {
            if (section._id) {
                this.sectionService
                    .update(section._id, Object.assign(res, { courseId: this.courseId }))
                    .then((resp) => {
                    if (resp.data) {
                        // section = resp.data;
                        const index = this.sections.findIndex((item) => item._id === resp.data._id);
                        this.sections[index] = resp.data;
                        this.sections = sortBy(this.sections, ['ordering']);
                        this.appService.toastSuccess('Updated successfully!');
                    }
                })
                    .catch((err) => {
                    this.appService.toastError(err);
                });
            }
            else {
                this.sectionService
                    .create(Object.assign(res, { courseId: this.courseId }))
                    .then((resp) => {
                    if (resp.data) {
                        this.sections.push(Object.assign(resp.data, { lectures: [] }));
                        this.sections = sortBy(this.sections, ['ordering']);
                        this.appService.toastSuccess('Created successfully!');
                    }
                })
                    .catch((err) => {
                    this.appService.toastError(err);
                });
            }
        }, () => {
            return;
        });
    }
    submitLecture(indexSection, sectionId, courseId, lecture = {}) {
        const modalRef = this.modalService.open(LectureFormComponent, {
            centered: true,
            backdrop: 'static',
            size: 'lg'
        });
        modalRef.componentInstance.lecture = lecture;
        modalRef.componentInstance.courseId = courseId;
        modalRef.result.then((res) => {
            if (lecture._id) {
                this.lectureService
                    .update(lecture._id, Object.assign(res, { sectionId }))
                    .then((resp) => {
                    if (resp.data) {
                        lecture = resp.data;
                        this.sections[indexSection].lectures = sortBy(this.sections[indexSection].lectures, ['ordering']);
                        this.appService.toastSuccess('Updated successfully!');
                    }
                })
                    .catch((err) => {
                    this.appService.toastError(err);
                });
            }
            else {
                this.lectureService
                    .create(Object.assign(res, { sectionId }))
                    .then((resp) => {
                    if (resp.data) {
                        this.sections[indexSection].lectures.push(resp.data);
                        this.sections[indexSection].totalLecture += 1;
                        this.sections[indexSection].lectures = sortBy(this.sections[indexSection].lectures, ['ordering']);
                        this.appService.toastSuccess('Created successfully!');
                    }
                })
                    .catch((err) => {
                    this.appService.toastError(err);
                });
            }
        }, () => {
            return;
        });
    }
    removeSection(item, index) {
        if (window.confirm('Are you sure to remove this section?')) {
            this.sectionService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.sections.splice(index, 1);
            })
                .catch((e) => this.appService.toastError(e));
        }
    }
    removeLecture(indexSection, item, index) {
        if (window.confirm('Are you sure to remove this lecture?')) {
            this.lectureService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.sections[indexSection].totalLecture -= 1;
                this.sections[indexSection].lectures.splice(index, 1);
            })
                .catch((e) => this.appService.toastError(e));
        }
    }
};
__decorate([
    Input()
], CourseLetureComponent.prototype, "course", void 0);
__decorate([
    Output()
], CourseLetureComponent.prototype, "doTabSelect", void 0);
CourseLetureComponent = __decorate([
    Component({
        selector: 'app-course-lecture',
        templateUrl: './course-lecture.html'
    })
], CourseLetureComponent);
export { CourseLetureComponent };
