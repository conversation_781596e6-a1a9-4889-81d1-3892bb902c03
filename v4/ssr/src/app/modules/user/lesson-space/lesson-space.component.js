var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let LessonSpaceComponent = class LessonSpaceComponent {
    constructor(seoService, authService, router, route, stateService) {
        this.seoService = seoService;
        this.authService = authService;
        this.router = router;
        this.route = route;
        this.stateService = stateService;
        this.tab = 'lesson-space';
        this.seoService.setMetaTitle('My Lesson');
        this.appointmentId = this.route.snapshot.queryParams['appointmentId'];
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
    }
    async ngOnInit() {
        if (this.currentUser && this.authService.isLoggedin()) {
            const stored = localStorage.getItem('lessonSpaceUrl');
            if (stored && stored.indexOf('room.sh') > -1) {
                const iframe = document.getElementById('lessonspace');
                iframe.setAttribute('src', stored);
            }
        }
    }
    back() {
        if (this.currentUser && this.currentUser.type === 'tutor') {
            this.router.navigate(['/users/appointments']);
        }
        else if (this.currentUser && this.currentUser.type === 'student') {
            this.router.navigate(['/users/lessons']);
        }
    }
};
LessonSpaceComponent = __decorate([
    Component({
        templateUrl: './iframe.html',
        styleUrls: ['./lesson-page.scss']
    })
], LessonSpaceComponent);
export { LessonSpaceComponent };
