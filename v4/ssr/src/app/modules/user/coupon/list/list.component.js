var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let CouponListComponent = class CouponListComponent {
    constructor(couponService, stateService, appService, seo) {
        this.couponService = couponService;
        this.stateService = stateService;
        this.appService = appService;
        this.seo = seo;
        this.items = [];
        this.searchFields = {};
        this.count = 0;
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = false;
        this.fromItem = 0;
        this.toItem = 0;
        this.seo.setMetaTitle('Coupons manager');
    }
    ngOnInit() {
        const currentUser = this.stateService.getState(STATE.CURRENT_USER);
        if (currentUser) {
            this.query();
        }
    }
    query() {
        this.loading = true;
        this.couponService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            ...this.searchFields
        })
            .then((resp) => {
            this.count = resp.data.count;
            this.items = resp.data.items;
            if (this.currentPage === 1) {
                this.fromItem = this.currentPage;
                this.toItem = this.items.length;
            }
            else if (this.currentPage > 1) {
                this.fromItem =
                    this.currentPage * this.pageSize > this.count
                        ? (this.currentPage - 1) * this.pageSize
                        : this.currentPage * this.pageSize;
                this.toItem = this.fromItem + this.items.length;
            }
            this.loading = false;
        })
            .catch((e) => {
            this.appService.toastError();
        });
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(sortOption) {
        this.sortOption = sortOption;
        this.query();
    }
    remove(item, index) {
        if (window.confirm('Are you sure to delete this coupon?')) {
            this.couponService
                .delete(item._id)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.items.splice(index, 1);
                this.count--;
            })
                .catch(() => this.appService.toastError());
        }
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 200);
    }
};
CouponListComponent = __decorate([
    Component({
        selector: 'app-coupon-list',
        templateUrl: './list.html'
    })
], CouponListComponent);
export { CouponListComponent };
