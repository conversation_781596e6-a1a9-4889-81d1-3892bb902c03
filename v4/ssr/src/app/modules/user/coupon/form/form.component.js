var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var NgbDateCustomParserFormatter_1;
import { Component, Input, Injectable } from '@angular/core';
import * as moment from 'moment';
import pick from 'lodash/pick';
import { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { STATE } from 'src/app/services';
function padNumber(value) {
    if (!isNaN(value) && value !== null) {
        return `0${value}`.slice(-2);
    }
    return '';
}
let NgbDateCustomParserFormatter = NgbDateCustomParserFormatter_1 = class NgbDateCustomParserFormatter extends NgbDateParserFormatter {
    parse(value) {
        if (value) {
            const dateParts = value.trim().split('/');
            const dateObj = {
                day: null,
                month: null,
                year: null
            };
            const dateLabels = Object.keys(dateObj);
            dateParts.forEach((datePart, idx) => {
                dateObj[dateLabels[idx]] =
                    parseInt(datePart, 10) || null;
            });
            return dateObj;
        }
        return null;
    }
    static formatDate(date) {
        return date
            ? `${padNumber(date.day)}/${padNumber(date.month)}/${date.year || ''}`
            : '';
    }
    format(date) {
        return NgbDateCustomParserFormatter_1.formatDate(date);
    }
};
NgbDateCustomParserFormatter = NgbDateCustomParserFormatter_1 = __decorate([
    Injectable()
], NgbDateCustomParserFormatter);
export { NgbDateCustomParserFormatter };
let CouponFormComponent = class CouponFormComponent {
    constructor(router, route, appService, couponService, stateService, webinarService, courseService, seo) {
        this.router = router;
        this.route = route;
        this.appService = appService;
        this.couponService = couponService;
        this.stateService = stateService;
        this.webinarService = webinarService;
        this.courseService = courseService;
        this.seo = seo;
        this.coupon = {
            name: '',
            code: '',
            type: 'percent',
            value: 0,
            webinarId: null,
            courseId: null,
            tutorId: null,
            expiredDate: '',
            active: true,
            startTime: '',
            limitNumberOfUse: 0,
            targetType: 'all'
        };
        this.time = {
            startTime: {
                day: 0,
                month: 0,
                year: 0
            },
            expiredDate: {
                day: 0,
                month: 0,
                year: 0
            }
        };
        this.validTime = {};
        this.isSubmitted = false;
        this.currentYear = new Date().getFullYear();
        this.webinars = [];
        this.courses = [];
        this.seo.setMetaTitle('Create new coupon');
        this.route.params.subscribe((params) => {
            if (params && params.id) {
                this.couponService
                    .findOne(params.id)
                    .then((resp) => {
                    this.coupon = resp.data;
                    const startTime = new Date(this.coupon.startTime);
                    const expiredDate = new Date(this.coupon.expiredDate);
                    this.time.startTime = {
                        year: startTime.getFullYear(),
                        month: startTime.getMonth() === 0 ? 12 : startTime.getMonth() + 1,
                        day: startTime.getDate()
                    };
                    this.time.expiredDate = {
                        year: expiredDate.getFullYear(),
                        month: expiredDate.getMonth() === 0 ? 12 : expiredDate.getMonth() + 1,
                        day: expiredDate.getDate()
                    };
                    if (this.coupon.targetType === 'webinar') {
                        this.queryWebinar();
                    }
                    if (this.coupon.targetType === 'course') {
                        this.queryCourse();
                    }
                })
                    .catch((err) => this.appService.toastError((err.data.data && err.data.data.message) ||
                    err.data.message ||
                    err.data.email));
            }
        });
    }
    ngOnInit() {
        this.tutor = this.stateService.getState(STATE.CURRENT_USER);
        if (this.tutor && this.tutor._id) {
            this.coupon.tutorId = this.tutor._id;
        }
    }
    selectDate(event, field = 'expiredDate') {
        const date = `${event.day}-${event.month}-${event.year}`;
        if (moment(date, 'DD/MM/YYYY')
            .add(30, 'second')
            .utc()
            .isBefore(moment().set('hour', 0).set('minute', 0).set('second', 0))) {
            this.validTime[field] = true;
            return this.appService.toastError('Please select date greater than or equal to the current date');
        }
        this.validTime[field] = false;
        this.coupon[field] = new Date(event.year, event.month - 1, event.day).toString();
        if (field === 'expiredDate' &&
            this.coupon.startTime &&
            this.coupon.expiredDate &&
            moment(this.coupon.startTime).isAfter(moment(this.coupon.expiredDate))) {
            this.validTime[field] = true;
            return this.appService.toastError('The expiration date must be greater than the start date');
        }
        else {
            this.coupon.expiredDate = moment(this.coupon.expiredDate)
                .set('hour', 23)
                .set('minute', 59)
                .set('second', 59)
                .toDate();
            this.validTime[field] = false;
        }
    }
    queryWebinar() {
        this.webinarService
            .search({
            take: 100,
            tutorId: this.coupon.tutorId
        })
            .then((resp) => {
            this.webinars = resp.data.items;
            // this.coupon.webinarId = this.webinars.length > 0 ? this.webinars[0]._id : null;
            if (this.coupon.targetType == 'webinar' &&
                ((this.webinars && this.webinars.length === 0) || !this.webinars)) {
                return this.appService.toastError("You don't have webinars");
            }
        })
            .catch((e) => this.appService.toastError(e));
    }
    queryCourse() {
        this.courseService
            .search({ take: 100, tutorId: this.coupon.tutorId })
            .then((resp) => {
            this.courses = resp.data.items;
            if ((this.courses && this.courses.length === 0) || !this.courses) {
                return this.appService.toastError("You don't have course");
            }
        })
            .catch((e) => this.appService.toastError(e));
    }
    onTargetTypeChange() {
        if (this.coupon.targetType === 'webinar') {
            this.coupon.courseId = null;
            this.queryWebinar();
        }
        else if (this.coupon.targetType === 'course') {
            this.coupon.webinarId = null;
            this.queryCourse();
        }
        else {
            this.coupon.webinarId = null;
            this.coupon.courseId = null;
        }
    }
    submit(frm) {
        this.isSubmitted = true;
        if (!frm.valid || !this.coupon.startTime || !this.coupon.expiredDate) {
            return this.appService.toastError('Invalid form, please try again.');
        }
        if (this.validTime['startTime'] || this.validTime['expiredDate']) {
            return this.appService.toastError('Invalid form, please enter valid date.');
        }
        if (!this.coupon._id) {
            this.couponService.create(this.coupon).then((resp) => {
                this.coupon = resp.data;
                this.appService.toastSuccess('Coupon has been created');
                this.router.navigate(['/users/coupons']);
            }, (err) => this.appService.toastError(err));
        }
        else {
            const data = pick(this.coupon, [
                'name',
                'code',
                'type',
                'value',
                'expiredDate',
                'tutorId',
                'webinarId',
                'courseId',
                'active',
                'startTime',
                'limitNumberOfUse',
                'targetType'
            ]);
            this.couponService.update(this.coupon._id, data).then(() => {
                this.router.navigate(['/users/coupons']);
                this.appService.toastSuccess('Coupon has been updated');
            }, (err) => this.appService.toastError(err));
        }
    }
};
__decorate([
    Input()
], CouponFormComponent.prototype, "tutor", void 0);
__decorate([
    Input()
], CouponFormComponent.prototype, "webinar", void 0);
__decorate([
    Input()
], CouponFormComponent.prototype, "targetType", void 0);
__decorate([
    Input()
], CouponFormComponent.prototype, "coupon", void 0);
CouponFormComponent = __decorate([
    Component({
        selector: 'app-coupon-form',
        templateUrl: './form.html'
    })
], CouponFormComponent);
export { CouponFormComponent };
