var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, HostListener } from '@angular/core';
import { STATE } from 'src/app/services';
let ListNotificationComponent = class ListNotificationComponent {
    constructor(seoService, notificationService, appService, stateService) {
        this.seoService = seoService;
        this.notificationService = notificationService;
        this.appService = appService;
        this.stateService = stateService;
        this.page = 1;
        this.pageSize = 10;
        this.items = [];
        this.total = 0;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = false;
        this.activeId = '';
        this.seoService.setMetaTitle('Notifications');
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
    }
    onClick(btn) {
        const { parentElement } = btn;
        if (!parentElement ||
            !parentElement.className ||
            (parentElement.className &&
                parentElement.className.indexOf('notification-option') < 0)) {
            this.activeId = '';
        }
    }
    ngOnInit() {
        this.query();
    }
    query() {
        this.loading = true;
        this.notificationService
            .list({
            page: this.page,
            take: this.pageSize,
            sort: 'updatedAt',
            sortType: 'desc'
        })
            .then((resp) => {
            this.items = resp.data.items;
            this.total = resp.data.count;
        });
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    selectNotificationOption(item) {
        if (this.activeId && this.activeId === item._id) {
            this.activeId = '';
            return;
        }
        this.activeId = item._id;
    }
    read(item, index, url = '', param = '') {
        if (item.unreadNotification > 0) {
            this.notificationService.read(item._id).then((resp) => {
                if (resp.data && resp.data.success) {
                    this.items[index].unreadNotification = 0;
                    this.notificationService.onReadNotificationSuccess(1);
                }
            });
        }
        // this.router.navigate(param ? [url, param] : [url]);
    }
    removeNotification(item, index) {
        if (window.confirm('Are you sure you want to delete this notification?')) {
            this.notificationService
                .remove(item._id)
                .then((resp) => {
                if (resp.data && resp.data.success) {
                    this.appService.toastSuccess('Removed successfully');
                    this.items.splice(index, 1);
                }
            })
                .catch((err) => this.appService.toastError(err));
        }
    }
};
__decorate([
    HostListener('click', ['$event.target'])
], ListNotificationComponent.prototype, "onClick", null);
ListNotificationComponent = __decorate([
    Component({
        templateUrl: 'list.html'
    })
], ListNotificationComponent);
export { ListNotificationComponent };
