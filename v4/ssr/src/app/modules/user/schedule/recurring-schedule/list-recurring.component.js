var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import * as moment from 'moment';
let ListRecurringScheduleComponent = class ListRecurringScheduleComponent {
    constructor(calendarService, toast, translate) {
        this.calendarService = calendarService;
        this.toast = toast;
        this.translate = translate;
        this.isFree = false;
        this.loading = false;
        this.filter = {
            currentPage: 1,
            pageSize: 10,
            sortOption: {
                sortBy: 'createdAt',
                sortType: 'desc'
            },
            total: 0
        };
        this.recurring = [];
        this.dayOfWeek = {
            1: 'Monday',
            2: 'Tuesday',
            3: 'Wednesday',
            4: 'Thursday',
            5: 'Friday',
            6: 'Saturday',
            0: 'Sunday'
        };
    }
    ngOnInit() {
        this.loadListSchedule();
    }
    loadListSchedule() {
        this.loading = true;
        const params = Object.assign({
            page: this.filter.currentPage,
            take: this.filter.pageSize,
            sort: `${this.filter.sortOption.sortBy}`,
            sortType: `${this.filter.sortOption.sortType}`,
            isFree: this.isFree
        });
        this.calendarService
            .loadListRecurring(params)
            .then(resp => {
            const { items, count } = resp.data;
            if (items && items.length > 0) {
                this.recurring = items;
                this.filter.total = count;
            }
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.toast.error('Can not get recurring!');
        });
    }
    sortBy(field, type) {
        this.filter.sortOption.sortBy = field;
        this.filter.sortOption.sortType = type;
        this.loadListSchedule();
    }
    onSort(evt) {
        this.filter.sortOption = evt;
        this.loadListSchedule();
    }
    renderDayOfWeek(dayOfWeek) {
        return dayOfWeek.map((item) => {
            const itemDay = this.dayOfWeek;
            return itemDay[item];
        }).join(', ');
    }
    renderTime(time) {
        const hour = moment(time, 'HH:mm').toDate().getHours();
        const minute = moment(time, 'HH:mm').toDate().getMinutes();
        return `${hour.toLocaleString('en-US', { minimumIntegerDigits: 2, useGrouping: false })}:${minute.toLocaleString('en-US', { minimumIntegerDigits: 2, useGrouping: false })}`;
    }
    remove(item, index) {
        this.calendarService
            .removeRecurring(item._id)
            .then(() => {
            this.toast.success(this.translate.instant(this.translate.instant('Recurring has been deleted!')));
            this.recurring.splice(index, 1);
        })
            .catch(e => this.toast.error(this.translate.instant(e.data.data.message)));
    }
};
__decorate([
    Input()
], ListRecurringScheduleComponent.prototype, "isFree", void 0);
ListRecurringScheduleComponent = __decorate([
    Component({
        selector: 'app-recurring-list',
        templateUrl: './list.html'
    })
], ListRecurringScheduleComponent);
export { ListRecurringScheduleComponent };
