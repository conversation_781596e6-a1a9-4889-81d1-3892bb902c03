var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import * as moment from 'moment';
let RecurringFormComponent = class RecurringFormComponent {
    constructor(appService, activeModal, calendarService) {
        this.appService = appService;
        this.activeModal = activeModal;
        this.calendarService = calendarService;
        this.isFree = false;
        this.slotDuration = 40;
        this.dayOfWeek = [
            {
                name: 'Monday',
                index: 1
            },
            {
                name: 'Tuesday',
                index: 2
            },
            {
                name: 'Wednesday',
                index: 3
            },
            {
                name: 'Thursday',
                index: 4
            },
            {
                name: 'Friday',
                index: 5
            },
            {
                name: 'Saturday',
                index: 6
            },
            {
                name: 'Sunday',
                index: 0
            }
        ];
        this.timeStart = { hour: 0, minute: 0 };
        this.timeEnd = { hour: 0, minute: 0 };
        this.range = {
            start: {
                day: 0,
                month: 0,
                year: 0
            },
            end: {
                day: 0,
                month: 0,
                year: 0
            }
        };
        this.isSubmitted = false;
        this.validTime = {};
        this.tab = 'list';
    }
    ngOnInit() {
        this.recurring = {
            start: '',
            end: '',
            range: {
                start: new Date(),
                end: new Date()
            },
            dayOfWeek: [],
            isFree: this.isFree
        };
        this.slotDuration =
            typeof this.slotDuration === 'string'
                ? parseInt(this.slotDuration, 10)
                : this.slotDuration;
    }
    selectDate(event, field) {
        const date = `${event.day}-${event.month}-${event.year}`;
        const validTimeObject = this.validTime;
        const rangeObject = this.recurring.range;
        if (moment(date, 'DD/MM/YYYY')
            .add(30, 'second')
            .utc()
            .isBefore(moment().set('hour', 0).set('minute', 0).set('second', 0))) {
            validTimeObject[field] = true;
            return this.appService.toastError('Please select end date greater than or equal to the start date');
        }
        validTimeObject[field] = false;
        rangeObject[field] = new Date(event.year, event.month - 1, event.day).toString();
        this.recurring.range = rangeObject;
        if (this.recurring.range.start &&
            this.recurring.range.end &&
            moment(this.recurring.range.start).isSameOrAfter(moment(this.recurring.range.end))) {
            validTimeObject[field] = true;
            return this.appService.toastError('The end date must be greater than the day to start');
        }
        else {
            this.recurring.range.start = moment(this.recurring.range.start)
                .set('hour', 0)
                .set('minute', 0)
                .set('second', 0)
                .toDate();
            this.recurring.range.end = moment(this.recurring.range.end)
                .set('hour', 23)
                .set('minute', 59)
                .set('second', 59)
                .toDate();
            validTimeObject[field] = false;
        }
        this.validTime = validTimeObject;
    }
    submit(frm) {
        try {
            this.isSubmitted = true;
            this.recurring.start = `${this.timeStart.hour}:${this.timeStart.minute}`;
            this.recurring.end = `${this.timeEnd.hour}:${this.timeEnd.minute}`;
            if (!frm.valid ||
                !this.recurring.range.start ||
                !this.recurring.range.end) {
                return this.appService.toastError('Invalid form, please try again.');
            }
            if (this.timeStart.hour === this.timeEnd.hour &&
                this.timeEnd.minute - this.timeStart.minute < this.slotDuration) {
                return this.appService.toastError(`Time allowed is ${this.slotDuration} minutes`);
            }
            if (this.timeEnd.hour < this.timeStart.hour) {
                if (this.timeStart.hour !== 23) {
                    return this.appService.toastError('Time end must be greater than start time');
                }
            }
            if (this.timeEnd.hour === this.timeStart.hour) {
                if (this.timeStart.minute - this.timeEnd.minute >= 0) {
                    return this.appService.toastError('Time end must be greater than start time');
                }
            }
            const minute = (moment(moment(this.recurring.end, 'HH:mm').toDate()).unix() -
                moment(moment(this.recurring.start, 'HH:mm').toDate()).unix()) /
                60;
            if (minute > this.slotDuration) {
                return this.appService.toastError(`Maximum time allowed is ${this.slotDuration} minutes!`);
            }
            this.calendarService.createRecurring(this.recurring).then((resp) => {
                this.appService.toastSuccess('Recurring events have been created');
                this.activeModal.close(resp.data);
            }, (err) => this.appService.toastError(err));
        }
        catch (error) {
            this.appService.toastError(error);
            this.activeModal.close(null);
        }
    }
    close(data) {
        this.activeModal.close(data);
    }
    changeTab(tab) {
        this.tab = tab;
    }
};
__decorate([
    Input()
], RecurringFormComponent.prototype, "isFree", void 0);
__decorate([
    Input()
], RecurringFormComponent.prototype, "slotDuration", void 0);
RecurringFormComponent = __decorate([
    Component({
        selector: 'app-recurring-from',
        templateUrl: './modal-recurring.html',
        styleUrls: ['./recurring.scss']
    })
], RecurringFormComponent);
export { RecurringFormComponent };
