var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, ViewChild } from '@angular/core';
import { RecurringFormComponent } from './recurring-schedule/modal-recurring/modal-recurring.component';
import { STATE } from 'src/app/services';
let ScheduleComponent = class ScheduleComponent {
    constructor(seoService, modalService, stateService) {
        this.seoService = seoService;
        this.modalService = modalService;
        this.stateService = stateService;
        this.tab = 'schedule';
        this.loading = false;
        this.seoService.setMetaTitle('My Schedule');
        this.tutor = this.stateService.getState(STATE.CURRENT_USER);
    }
    onTabSelect(tab) {
        this.tab = tab;
        if (tab === 'schedule') {
            setTimeout(() => {
                this.schedulePaid.reRender();
            }, 100);
        }
        else if (tab === 'free') {
            setTimeout(() => {
                this.scheduleFree.reRender();
            }, 100);
        }
    }
    onChange(isFree) {
        if (!isFree) {
            this.scheduleFree.loadStatic();
        }
        else {
            this.schedulePaid.loadStatic();
        }
    }
    addRecurring(isFree) {
        const recurringModalRef = this.modalService.open(RecurringFormComponent, {
            size: 'lg',
            centered: true,
            backdrop: 'static'
        });
        recurringModalRef.componentInstance.isFree = isFree;
        recurringModalRef.componentInstance.slotDuration = this.tutor.defaultSlotDuration;
        recurringModalRef.result.then(() => {
            this.scheduleFree.loadStatic();
            this.schedulePaid.loadStatic();
        }, () => { return; });
    }
};
__decorate([
    ViewChild('schedulePaid')
], ScheduleComponent.prototype, "schedulePaid", void 0);
__decorate([
    ViewChild('scheduleFree')
], ScheduleComponent.prototype, "scheduleFree", void 0);
ScheduleComponent = __decorate([
    Component({
        templateUrl: './schedule.html'
    })
], ScheduleComponent);
export { ScheduleComponent };
