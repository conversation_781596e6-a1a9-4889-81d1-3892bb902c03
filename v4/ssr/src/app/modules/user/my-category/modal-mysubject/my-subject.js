var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { pick } from 'lodash';
let MySubjectFormComponent = class MySubjectFormComponent {
    constructor(toasty, activeModal, subjectService, translate) {
        this.toasty = toasty;
        this.activeModal = activeModal;
        this.subjectService = subjectService;
        this.translate = translate;
        this.submitted = false;
    }
    ngOnInit() {
        if (this.selectedCategory) {
            this.querySubjects();
        }
    }
    submit(frm) {
        this.submitted = true;
        if (!frm.valid) {
            return this.toasty.error(this.translate.instant('Please complete the required fields!'));
        }
        return this.activeModal.close(pick(this.mySubject, ['isActive', 'originalSubjectId']));
    }
    querySubjects() {
        this.subjectService.search({ categoryIds: this.selectedCategory.originalCategoryId, take: 1000 }).then(resp => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.subjects = resp.data.items;
            }
            else {
                this.subjects = [];
            }
        });
    }
};
__decorate([
    Input()
], MySubjectFormComponent.prototype, "subjects", void 0);
__decorate([
    Input()
], MySubjectFormComponent.prototype, "mySubject", void 0);
__decorate([
    Input()
], MySubjectFormComponent.prototype, "selectedCategory", void 0);
MySubjectFormComponent = __decorate([
    Component({
        selector: 'app-my-subject-form',
        templateUrl: './my-subject.html'
    })
], MySubjectFormComponent);
export { MySubjectFormComponent };
