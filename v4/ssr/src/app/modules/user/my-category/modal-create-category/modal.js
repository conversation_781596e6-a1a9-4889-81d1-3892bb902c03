var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { pick } from 'lodash';
let MyCategoryFormComponent = class MyCategoryFormComponent {
    constructor(toasty, activeModal, translate) {
        this.toasty = toasty;
        this.activeModal = activeModal;
        this.translate = translate;
        this.myCategory = {
            isActive: true
        };
        this.submitted = false;
    }
    submit(frm) {
        this.submitted = true;
        if (!frm.valid) {
            return this.toasty.error(this.translate.instant('Please complete the required fields!'));
        }
        return this.activeModal.close(pick(this.myCategory, ['originalCategoryId', 'isActive']));
    }
};
__decorate([
    Input()
], MyCategoryFormComponent.prototype, "categories", void 0);
__decorate([
    Input()
], MyCategoryFormComponent.prototype, "myCategory", void 0);
MyCategoryFormComponent = __decorate([
    Component({
        selector: 'app-modal-create-category',
        templateUrl: './form.html'
    })
], MyCategoryFormComponent);
export { MyCategoryFormComponent };
