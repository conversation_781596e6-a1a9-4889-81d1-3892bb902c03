var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { pick } from 'lodash';
let MyTopicFormComponent = class MyTopicFormComponent {
    constructor(toasty, translate, activeModal, topicService) {
        this.toasty = toasty;
        this.translate = translate;
        this.activeModal = activeModal;
        this.topicService = topicService;
        this.myTopic = {
            isActive: true
        };
        this.submitted = false;
    }
    ngOnInit() {
        if (this.selectedSubject) {
            this.queryTopic();
        }
    }
    submit(frm) {
        this.submitted = true;
        if (this.myTopic.price && this.myTopic.price <= 0) {
            return this.toasty.error(this.translate.instant('Price must be equal or greater than 0!'));
        }
        if (!frm.valid) {
            return this.toasty.error(this.translate.instant('Please complete the required fields!'));
        }
        return this.activeModal.close(pick(this.myTopic, ['originalTopicId', 'isActive', 'price']));
    }
    queryTopic() {
        this.topicService.search({ subjectIds: this.selectedSubject.originalSubjectId, take: 1000 }).then(resp => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.topics = resp.data.items;
            }
            else {
                this.topics = [];
            }
        });
    }
};
__decorate([
    Input()
], MyTopicFormComponent.prototype, "topics", void 0);
__decorate([
    Input()
], MyTopicFormComponent.prototype, "myTopic", void 0);
__decorate([
    Input()
], MyTopicFormComponent.prototype, "selectedSubject", void 0);
MyTopicFormComponent = __decorate([
    Component({
        selector: 'app-modal-create-category',
        templateUrl: './form.html'
    })
], MyTopicFormComponent);
export { MyTopicFormComponent };
