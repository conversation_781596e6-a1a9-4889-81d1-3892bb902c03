var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { studentMenus, tutorMenus } from './menu';
import { chunk } from 'lodash';
import { STATE } from 'src/app/services';
let DashboardComponent = class DashboardComponent {
    constructor(seoService, stateService) {
        this.seoService = seoService;
        this.stateService = stateService;
        this.type = '';
        this.studentMenus = chunk(studentMenus, 4);
        this.tutorMenus = chunk(tutorMenus, 4);
        this.setBg = () => {
            let randomColor = '';
            do {
                randomColor = Math.floor(Math.random() * 16777215).toString(16);
            } while (!CSS.supports('color', '#' + randomColor));
            return '#' + randomColor;
        };
        this.seoService.setMetaTitle('Dashboard');
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        this.type = this.currentUser.type;
    }
    setBackgroundIcon(index) {
        return index % 2 == 0 ? '#f5b33f' : '#072e6f';
    }
    setColorIcon(index) {
        return index % 2 == 0 ? '#072e6f' : '#f5b33f';
    }
    isColor(strColor) {
        const s = new Option().style;
        s.color = strColor;
        return s.color == strColor;
    }
    ngAfterViewInit() {
        switch (this.type) {
            case 'student':
                for (const menu of studentMenus) {
                    if (menu.key) {
                        const menuElement = document.getElementById(menu.key);
                        if (menuElement) {
                            menuElement.style.backgroundColor = this.setBg();
                        }
                    }
                }
                break;
            case 'tutor':
                for (const menu of tutorMenus) {
                    if (menu.key) {
                        const menuElement = document.getElementById(menu.key);
                        if (menuElement) {
                            menuElement.style.backgroundColor = this.setBg();
                        }
                    }
                }
                break;
            default:
                break;
        }
    }
};
DashboardComponent = __decorate([
    Component({
        templateUrl: './dashboard.html'
    })
], DashboardComponent);
export { DashboardComponent };
