var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, ViewChild, Output, EventEmitter } from '@angular/core';
import * as _ from 'lodash';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
import { AvatarUploadComponent } from 'src/app/components/media/avatar-upload/avatar-upload.component';
import { MyCertificateComponent } from 'src/app/components/user/my-certificate-modal/my-certificate.component';
import { AddCetificationComponent } from 'src/app/components/tutor/certificate/add-certification/add-certification.component';
import { quillConfig } from 'src/app/lib';
let ProfileUpdateComponent = class ProfileUpdateComponent {
    constructor(authService, userService, route, seoService, tutorService, gradeService, languageService, countryService, utilService, modalService, mySubjectService, myCourseService, appService, stateService) {
        this.authService = authService;
        this.userService = userService;
        this.route = route;
        this.seoService = seoService;
        this.tutorService = tutorService;
        this.gradeService = gradeService;
        this.languageService = languageService;
        this.countryService = countryService;
        this.utilService = utilService;
        this.modalService = modalService;
        this.mySubjectService = mySubjectService;
        this.myCourseService = myCourseService;
        this.appService = appService;
        this.stateService = stateService;
        this.avatarUrl = '';
        this.isSubmitted = false;
        this.avatarOptions = {};
        this.uploading = false;
        this.afterCancel = new EventEmitter();
        this.isEditProfile = false;
        this.isEditDescription = false;
        this.isEditGrade = false;
        this.isEditSubject = false;
        this.languageNames = [];
        this.objectLanguage = {};
        this.gradeNames = [];
        this.totalUserGrades = 0;
        this.subjects = [];
        this.tutorSubjects = [];
        this.emailInvite = '';
        this.loading = false;
        this.showChar = 500;
        this.showMore = false;
        this.myCompletedCourses = [];
        this.introVideoType = 'upload';
        this.introVideoName = '';
        this.quillConfig = quillConfig;
        this.banner = 'url(assets/images/dashboard/bg-profile.svg)';
        this.active = 1;
        this.subjects = this.route.snapshot.data['subjects'];
        this.seoService.setMetaTitle('Profile');
        this.webUrl = environment.url;
    }
    async ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
        if (this.config && this.config.profileBanner) {
            this.banner = `url(${this.config.profileBanner})`;
        }
        this.loading = true;
        this.countries = this.countryService.getCountry();
        this.languages = this.languageService.getLang();
        this.objectLanguage = this.languageService.languages;
        this.avatarOptions = {
            url: environment.apiBaseUrl + '/users/avatar',
            fileFieldName: 'avatar',
            onFinish: (resp) => {
                this.avatarUrl = resp.data.url;
            }
        };
        this.introVideoOptions = {
            url: environment.apiBaseUrl + '/tutors/upload-introVideo',
            fileFieldName: 'file',
            onFinish: (resp) => {
                this.info.introVideoId = resp.data._id;
                this.introVideoName = resp.data.name;
                this.uploading = false;
            },
            onFileSelect: (resp) => (this.introVideo = resp[0].file),
            id: 'id-introVideo',
            accept: 'video/*',
            onUploading: () => (this.uploading = true)
        };
        await this.userService.me().then((resp) => {
            this.info = resp.data;
            if (this.info && this.info.bio && this.info.bio.length > this.showChar) {
                this.showMore = true;
            }
            if (this.info.type === 'tutor') {
                this.introVideoType = this.info.introVideoId ? 'upload' : 'youtube';
            }
            if (this.info.introVideo) {
                this.introVideoName = this.info.introVideo.name;
            }
            const params = {
                tutorId: this.info._id
            };
            this.mySubjectService.search(params).then((res) => {
                this.tutorSubjects = res.data.items;
            });
            this.userId = this.info._id;
            this.avatarUrl = resp.data.avatarUrl;
            if (this.avatarUrl !== 'http://localhost:9000/assets/default-avatar.jpg')
                this.checkAvatar = true;
        });
        await this.myCourseService
            .search({ isCompleted: true, sort: 'completedAt', sortType: 'desc' })
            .then((resp) => {
            this.myCompletedCourses = resp.data.items;
        });
        await this.gradeService
            .search({
            take: 100,
            sort: 'ordering',
            sortType: 'asc'
        })
            .then((resp) => {
            this.grades = resp.data.items;
        });
        this.mapGradeName(this.info.grades);
        this.mapLanguageName(this.info.languages);
        this.loading = false;
    }
    mapGradeName(gradeKeys) {
        this.grades.forEach((key) => {
            if (gradeKeys.indexOf(key.id) > -1) {
                this.gradeNames.push(key.name);
            }
        });
        this.totalUserGrades = this.gradeNames.length;
        if (this.totalUserGrades > 4)
            this.gradeNames = _.chunk(this.gradeNames);
    }
    mapLanguageName(languageKeys) {
        languageKeys.forEach((key) => {
            this.languageNames.push(this.objectLanguage[key]);
        });
    }
    changeTimezone(event) {
        if (event === 'Asia/Saigon') {
            this.info.timezone = 'Asia/Ho_Chi_Minh';
        }
        else {
            this.info.timezone = event;
        }
    }
    submit(frm, isSubmitForm = true) {
        if (isSubmitForm) {
            this.isSubmitted = true;
            if (this.info.type === 'tutor') {
                if (!this.info.introVideoId && !this.info.introYoutubeId)
                    return this.appService.toastError('Please upload introduction video');
            }
            if (this.introVideoType === 'youtube') {
                this.info.introVideoId = null;
            }
            else
                this.info.introYoutubeId = '';
            if (!frm.valid || !this.info.timezone) {
                return this.appService.toastError('Form is invalid, please check again.');
            }
            this.isEditProfile = false;
            this.isEditDescription = false;
        }
        if (this.info.type === 'tutor') {
            const data = _.pick(this.info, [
                'name',
                'username',
                'subjectIds',
                'bio',
                'email',
                'address',
                'phoneNumber',
                'grades',
                'languages',
                'password',
                'timezone',
                'gender',
                'zipCode',
                'price1On1Class',
                'idYoutube',
                'country',
                'city',
                'state',
                'introYoutubeId',
                'introVideoId',
                'defaultSlotDuration',
                'paypalEmailId'
            ]);
            return this.tutorService
                .update(data)
                .then((resp) => {
                console.log(resp);
                this.info = _.merge(resp.data, this.info);
                this.languageNames = [];
                this.mapLanguageName(this.info.languages);
                this.gradeNames = [];
                this.mapGradeName(this.info.grades);
                this.appService.toastSuccess('Updated successfully!');
                this.utilService.notifyEvent('profileUpdate', this.info);
                this.authService.updateCurrentUser(this.info);
                localStorage.setItem('timeZone', this.info.timezone);
                this.stateService.saveState(STATE.CURRENT_USER, this.info);
            })
                .catch((err) => this.appService.toastError(err));
        }
        return this.userService
            .updateMe(this.info)
            .then((resp) => {
            this.info = _.merge(resp.data, this.info);
            this.appService.toastSuccess('Updated successfully!');
            this.utilService.notifyEvent('profileUpdate', this.info);
            localStorage.setItem('timeZone', this.info.timezone);
            this.stateService.saveState(STATE.CURRENT_USER, this.info);
        })
            .catch((err) => this.appService.toastError(err));
    }
    changeNotification() {
        this.info.notificationSettings = !this.info.notificationSettings;
        const data = _.pick(this.info, ['notificationSettings']);
        this.userService
            .updateMe(data)
            .then((resp) => {
            this.info = _.merge(resp.data, this.info);
            if (this.info.notificationSettings === true) {
                this.appService.toastSuccess('Notification activated successfully!');
            }
            if (this.info.notificationSettings === false) {
                this.appService.toastSuccess('Notification deactivated successfully!');
            }
        })
            .catch((err) => this.appService.toastError(err));
    }
    inviteFriend() {
        this.userService
            .inviteFriend({ email: this.emailInvite })
            .then((resp) => {
            if (resp.data.success) {
                return this.appService.toastSuccess('Invited Successfully!');
            }
            return this.appService.toastError('Invite fail');
        });
    }
    onChangeLanguage(e) {
        this.ngSelectComponent.clearAllText = '';
    }
    onChangeGrade(event) {
        this.info.grades = [];
        event.forEach((element) => {
            this.info.grades.push(element.id);
        });
        this.submit('', false);
    }
    openChangeAvatarModal() {
        const modalRef = this.modalService.open(AvatarUploadComponent, {
            centered: true,
            backdrop: 'static'
        });
        modalRef.componentInstance.info = this.info;
        modalRef.result.then((res) => {
            this.afterCancel.emit(res);
            this.info.avatarUrl = res;
            this.checkAvatar = true;
        });
    }
    openCertification(type, index = 0, certificate = null) {
        const modalRef = this.modalService.open(AddCetificationComponent, {
            centered: true,
            backdrop: 'static'
        });
        modalRef.componentInstance.tutorId = this.info._id;
        modalRef.componentInstance.certificate = certificate;
        modalRef.componentInstance.type = type || 'education';
        modalRef.result.then((res) => {
            const plotOptions = this.info;
            if (certificate) {
                plotOptions[type][index] = res;
            }
            else {
                plotOptions[type].push(res);
            }
            this.info = plotOptions;
        });
    }
    deleteCer(type, index, certificate = null) {
        if (window.confirm('Are you sure to delete this certificate?') &&
            certificate) {
            this.tutorService
                .deleteCertificate(certificate._id)
                .then(() => {
                this.appService.toastSuccess('Deleted certificate successfully');
            })
                .catch((err) => {
                this.appService.toastError(err);
            });
        }
    }
    deleteAvatar() {
        if (this.checkAvatar) {
            if (window.confirm('Are you sure to delete your avatar?')) {
                this.userService
                    .deleteAvatar()
                    .then(() => {
                    this.info.avatarUrl =
                        'http://localhost:9000/assets/default-avatar.jpg';
                    this.appService.toastSuccess('Delete avatar successfully');
                    this.checkAvatar = false;
                })
                    .catch((err) => {
                    this.appService.toastError(err);
                });
            }
        }
        else {
            this.appService.toastError('No avatar to delete!');
        }
    }
    viewCertificate(myCourse) {
        const modalRef = this.modalService.open(MyCertificateComponent, {
            centered: true,
            backdrop: 'static',
            size: 'xl'
        });
        modalRef.componentInstance.myCourse = myCourse;
        modalRef.componentInstance.userName = this.info.name;
        modalRef.componentInstance.appConfig = this.config;
    }
    submitChangeEmail() {
        if (window.confirm(`Are you sure to change your email to ${this.info.email}? We will send you an email to verify your new email, please verify it to continue using the site.`))
            this.userService
                .changeEmail(this.info._id, { email: this.info.email })
                .then(() => {
                this.appService.toastSuccess('An email has been sent to your new email address, please verify it.');
                this.authService.removeToken();
                window.location.href = '/';
            })
                .catch((err) => {
                this.appService.toastError(err);
            });
    }
};
__decorate([
    ViewChild('language')
], ProfileUpdateComponent.prototype, "ngSelectComponent", void 0);
__decorate([
    Output()
], ProfileUpdateComponent.prototype, "afterCancel", void 0);
ProfileUpdateComponent = __decorate([
    Component({
        selector: 'app-profile-update',
        templateUrl: './form.html'
    })
], ProfileUpdateComponent);
export { ProfileUpdateComponent };
