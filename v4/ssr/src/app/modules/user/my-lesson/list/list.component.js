var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { FilterByDate } from '../../my-schedule/list/list.component';
import * as moment from 'moment';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
import { ModalAppointmentComponent } from '../../modal-appointment/modal-appointment.component';
import { encrypt } from 'src/app/lib';
import { pick } from 'lodash';
let ListLessonComponent = class ListLessonComponent {
    constructor(auth, appointmentService, seoService, appService, transactionService, modalService, router, stateService) {
        this.auth = auth;
        this.appointmentService = appointmentService;
        this.seoService = seoService;
        this.appService = appService;
        this.transactionService = transactionService;
        this.modalService = modalService;
        this.router = router;
        this.stateService = stateService;
        this.currentPage = 1;
        this.pageSize = 10;
        this.total = 2;
        this.searchFields = {};
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.appointments = [];
        this.count = 0;
        this.loading = false;
        this.joining = false;
        this.tab = 'subject';
        this.filterTransactionOptions = {
            currentPage: 1,
            pageSize: 10,
            sortOption: {
                sortBy: 'createdAt',
                sortType: 'desc'
            },
            total: 0,
            loading: false,
            searchFields: {}
        };
        this.transactions = [];
        this.filterBy = FilterByDate.all_day;
        this.seoService.setMetaTitle('My Lessons');
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        if (this.auth.isLoggedin()) {
            this.auth.getCurrentUser().then((resp) => {
                this.currentUser = resp;
                this.query();
            });
        }
    }
    async query() {
        this.loading = true;
        await this.appointmentService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            userId: this.currentUser._id,
            targetType: this.tab,
            paid: true,
            ...this.searchFields
        })
            .then((resp) => {
            this.count = resp.data.count;
            this.appointments = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        if (this.tab === 'subject') {
            this.query();
        }
        else {
            this.queryAppointmentWebinar();
        }
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.description = searchText;
            this.query();
        }, 400);
    }
    joinMeeting(appointmentId) {
        if (!this.joining) {
            this.joining = true;
            this.appointmentService
                .joinMeeting(appointmentId)
                .then((resp) => {
                this.joining = false;
                if (resp.data &&
                    resp.data.platform === 'zoomus' &&
                    resp.data['zoomus'].signature) {
                    const token = encrypt({
                        meetingInfo: resp.data['zoomus'],
                        appointmentId,
                        currentUser: pick(this.currentUser, ['name', 'email', 'type'])
                    }, '');
                    window.location.href = `${environment.zoomSiteUrl}?token=${encodeURIComponent(token)}`;
                }
                else if (resp.data &&
                    resp.data.platform === 'lessonspace' &&
                    resp.data['lessonspace'].url) {
                    localStorage.setItem('lessonSpaceUrl', resp.data['lessonspace'].url);
                    this.router.navigate(['/users/lesson-space'], {
                        queryParams: {
                            appointmentId
                        }
                    });
                }
            })
                .catch((err) => {
                this.joining = false;
                return this.appService.toastError(err);
            });
        }
        else {
            this.appService.toastSuccess('Connecting...');
        }
    }
    onTabSelect(tab) {
        this.tab = tab;
        if (this.tab === 'subject') {
            this.query();
        }
        else
            this.queryAppointmentWebinar();
    }
    queryTransactionWebinar() {
        if (this.auth.isLoggedin()) {
            this.auth.getCurrentUser().then((resp) => {
                this.currentUser = resp;
                this.loading = true;
                this.transactionService
                    .search({
                    page: this.filterTransactionOptions.currentPage,
                    take: this.filterTransactionOptions.pageSize,
                    sort: `${this.filterTransactionOptions.sortOption.sortBy}`,
                    sortType: `${this.filterTransactionOptions.sortOption.sortType}`,
                    userId: this.currentUser._id,
                    targetType: 'webinar'
                    // status: 'completed'
                })
                    .then((res) => {
                    this.transactions = res.data.items;
                    this.filterTransactionOptions.total = res.data.count;
                    this.loading = false;
                })
                    .catch((err) => {
                    this.loading = false;
                    return this.appService.toastError(err);
                });
            });
        }
    }
    queryAppointmentWebinar() {
        if (this.auth.isLoggedin()) {
            this.auth.getCurrentUser().then((resp) => {
                this.currentUser = resp;
                this.loading = true;
                this.appointmentService
                    .searchAppointmentWebinar({
                    page: this.filterTransactionOptions.currentPage,
                    take: this.filterTransactionOptions.pageSize,
                    sort: `${this.filterTransactionOptions.sortOption.sortBy}`,
                    sortType: `${this.filterTransactionOptions.sortOption.sortType}`,
                    tutorId: this.currentUser._id,
                    targetType: this.tab,
                    ...this.searchFields
                })
                    .then((res) => {
                    this.transactions = res.data.items;
                    this.filterTransactionOptions.total = res.data.count;
                    this.loading = false;
                })
                    .catch(() => {
                    this.loading = false;
                    this.appService.toastError();
                });
            });
        }
    }
    openModalAppointment(transaction) {
        if (transaction.webinar) {
            const modalRef = this.modalService.open(ModalAppointmentComponent, {
                centered: true,
                backdrop: 'static',
                size: 'lg'
            });
            modalRef.componentInstance.currentUser = this.currentUser;
            modalRef.componentInstance.webinar = transaction.webinar;
            modalRef.componentInstance.type = 'student';
            // modalRef.componentInstance.transactionId = transaction._id;
            modalRef.result.then(() => {
                return;
            }, () => {
                return;
            });
        }
    }
    filterByDate() {
        switch (this.filterBy) {
            case FilterByDate.all_day:
                delete this.searchFields.startTime;
                delete this.searchFields.toTime;
                this.tab === 'subject' ? this.query() : this.queryAppointmentWebinar();
                break;
            case FilterByDate.today:
                this.searchFields.startTime = moment().startOf('day').toISOString();
                this.searchFields.toTime = moment().endOf('day').toISOString();
                this.tab === 'subject' ? this.query() : this.queryAppointmentWebinar();
                break;
            case FilterByDate.this_week:
                this.searchFields.startTime = moment().startOf('week').toISOString();
                this.searchFields.toTime = moment().endOf('week').toISOString();
                this.tab === 'subject' ? this.query() : this.queryAppointmentWebinar();
                break;
            default:
                break;
        }
    }
};
ListLessonComponent = __decorate([
    Component({
        selector: 'app-list-lesson',
        templateUrl: './list.html'
    })
], ListLessonComponent);
export { ListLessonComponent };
