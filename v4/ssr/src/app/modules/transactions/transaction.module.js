var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ListTransactionComponent, AppointmentDetailComponent } from './components';
import { TranslateModule } from '@ngx-translate/core';
import { SortComponent } from 'src/app/components/uis/sort/sort.component';
import { CourseTransactionListComponent } from './components/course-transaction/list/list.component';
import categoriesResolver from 'src/app/services/resolvers/category.resolver';
import { CourseTransactionDetailComponent } from './components/course-transaction/detail/detail.component';
import { SharedModule } from 'src/app/shared.module';
const routes = [
    {
        path: '',
        component: ListTransactionComponent,
        resolve: {}
    },
    {
        path: ':id/view',
        component: AppointmentDetailComponent,
        resolve: {}
    },
    {
        path: 'course-transaction',
        component: CourseTransactionListComponent,
        resolve: {
            categories: categoriesResolver
        }
    },
    {
        path: 'course-transaction/:id',
        component: CourseTransactionDetailComponent,
        resolve: {}
    }
];
let TransactionModule = class TransactionModule {
};
TransactionModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            RouterModule.forChild(routes),
            FormsModule,
            ReactiveFormsModule,
            NgbModule,
            TranslateModule.forChild(),
            SortComponent,
            SharedModule
        ],
        declarations: [
            ListTransactionComponent,
            AppointmentDetailComponent,
            CourseTransactionDetailComponent,
            CourseTransactionListComponent
        ],
        exports: [ListTransactionComponent, AppointmentDetailComponent]
    })
], TransactionModule);
export { TransactionModule };
