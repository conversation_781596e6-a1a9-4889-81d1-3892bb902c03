var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let ListTransactionComponent = class ListTransactionComponent {
    constructor(appService, seoService, transactionService, stateService) {
        this.appService = appService;
        this.seoService = seoService;
        this.transactionService = transactionService;
        this.stateService = stateService;
        this.page = 1;
        this.pageSize = 10;
        this.total = 8;
        this.searchFields = {
            targetType: '',
            status: ''
        };
        this.transaction = [];
        this.loading = false;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.columns = [
            {
                title: 'Tutor name',
                dataIndex: 'tutor',
                sorter: true,
                sortBy: 'tutorId'
            },
            {
                title: 'Type',
                dataIndex: 'type',
                sorter: true,
                sortBy: 'type'
            },
            {
                title: 'Code',
                dataIndex: 'code',
                sorter: true,
                sortBy: 'code'
            }
        ];
        this.seoService.setMetaTitle('My Transactions');
        this.loading = true;
        this.config = this.stateService.getState(STATE.CONFIG);
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
    }
    ngOnInit() {
        this.userId = this.currentUser._id;
        this.type = this.currentUser.type;
        this.query();
    }
    query() {
        this.loading = true;
        this.transactionService
            .search({
            userId: this.userId,
            page: this.page,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            ...this.searchFields
        })
            .then((resp) => {
            this.transaction = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch((err) => {
            this.loading = false;
            return this.appService.toastError(err);
        });
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
};
ListTransactionComponent = __decorate([
    Component({
        selector: 'app-list-transactions',
        templateUrl: './list.html'
    })
], ListTransactionComponent);
export { ListTransactionComponent };
