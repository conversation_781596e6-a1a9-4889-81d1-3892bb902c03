var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let CourseTransactionDetailComponent = class CourseTransactionDetailComponent {
    constructor(route, transactionService, authService, stateService) {
        this.route = route;
        this.transactionService = transactionService;
        this.authService = authService;
        this.stateService = stateService;
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.transactionId = this.route.snapshot.paramMap.get('id');
        this.authService.getCurrentUser().then(resp => {
            this.tutorId = resp._id;
            this.transactionService.findOneTransactionCourse(this.tutorId, this.transactionId).then((res) => {
                this.transaction = res.data;
            });
        });
    }
};
CourseTransactionDetailComponent = __decorate([
    Component({
        templateUrl: './detail.html'
    })
], CourseTransactionDetailComponent);
export { CourseTransactionDetailComponent };
