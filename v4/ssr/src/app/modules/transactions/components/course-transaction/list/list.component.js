var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let CourseTransactionListComponent = class CourseTransactionListComponent {
    constructor(courseService, auth, appService, seoService, route, stateService) {
        this.courseService = courseService;
        this.auth = auth;
        this.appService = appService;
        this.seoService = seoService;
        this.route = route;
        this.stateService = stateService;
        this.transactions = [];
        this.page = 1;
        this.pageSize = 10;
        this.total = 0;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.categories = [];
        this.searchFields = {
            categoryIds: '',
            name: ''
        };
        this.loading = false;
        this.seoService.setMetaTitle('Course Transactions');
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.categories = this.route.snapshot.data['categories'];
        this.auth.getCurrentUser().then((resp) => {
            this.tutorId = resp._id;
            this.query();
        });
    }
    query() {
        this.loading = true;
        this.courseService
            .getTransactions(this.tutorId, {
            userId: this.tutorId,
            page: this.page,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`
        })
            .then((resp) => {
            this.transactions = resp.data.items;
            const data = this.transactions.slice();
            if (this.searchFields.categoryIds) {
                this.transactions.length = 0;
                data.forEach((item) => {
                    if (item.course.categoryIds &&
                        item.course.categoryIds.includes(this.searchFields.categoryIds)) {
                        this.transactions.push(item);
                    }
                });
                if (this.searchFields.name) {
                    //this.transactions.length = 0;
                    data.forEach((item) => {
                        if (item.course.name &&
                            !item.course.name
                                .toLowerCase()
                                .includes(this.searchFields.name.toLowerCase())) {
                            this.transactions.splice(this.transactions.indexOf(item), 1);
                        }
                    });
                }
            }
            else {
                if (this.searchFields.name) {
                    this.transactions.length = 0;
                    data.forEach((item) => {
                        if (item.course.name &&
                            item.course.name
                                .toLowerCase()
                                .includes(this.searchFields.name.toLowerCase())) {
                            this.transactions.push(item);
                        }
                    });
                }
            }
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch((err) => {
            this.loading = false;
            return this.appService.toastError(err);
        });
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
};
CourseTransactionListComponent = __decorate([
    Component({
        templateUrl: './list.html'
    })
], CourseTransactionListComponent);
export { CourseTransactionListComponent };
