var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let AppointmentDetailComponent = class AppointmentDetailComponent {
    constructor(route, router, appService, transactionService, refundService, location, stateService) {
        this.route = route;
        this.router = router;
        this.appService = appService;
        this.transactionService = transactionService;
        this.refundService = refundService;
        this.location = location;
        this.stateService = stateService;
        this.transaction = {};
        this.options = {
            transactionId: '',
            type: 'appointment',
            tutorId: '',
            userId: ''
        };
        this.submitted = false;
        this.reason = '';
        this.config = this.stateService.getState(STATE.CONFIG);
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
    }
    ngOnInit() {
        this.aId = this.route.snapshot.paramMap.get('id');
        this.type = this.currentUser.type;
        this.findOne();
    }
    findOne() {
        this.transactionService.findOne(this.aId).then((resp) => {
            this.transaction = resp.data;
            this.options.transactionId = this.transaction._id;
            this.options.tutorId =
                this.transaction.tutor && this.transaction.tutor._id;
            this.options.userId = this.transaction.user && this.transaction.user._id;
        });
    }
    cancelEvent(info) {
        if (!info && info.status !== 'canceled') {
            return this.appService.toastError();
        }
        this.transaction.status = 'canceled';
    }
    request(type) {
        this.submitted = true;
        if (this.reason.trim() === '') {
            return this.appService.toastError('Please enter reason');
        }
        if (!this.currentUser.paypalEmailId) {
            this.appService.toastError('Please update your paypal email Id before send refund request');
            return this.router.navigate(['/users/profile']);
        }
        this.refundService
            .create({
            transactionId: this.transaction._id,
            reason: this.reason,
            type,
            targetType: this.transaction.targetType
        })
            .then(() => {
            this.location.back();
            this.appService.toastSuccess('Request successfully!');
        })
            .catch((e) => this.appService.toastError(e));
    }
};
AppointmentDetailComponent = __decorate([
    Component({
        selector: 'app-detail-appointment',
        templateUrl: './detail.html'
    })
], AppointmentDetailComponent);
export { AppointmentDetailComponent };
