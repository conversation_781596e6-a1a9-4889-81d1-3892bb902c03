var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HomeComponent } from './home.component';
import { SearchBarComponent } from 'src/app/components/home/<USER>/search-bar.component';
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { CardCourseComponent } from 'src/app/components/course/card-course/card-course.component';
import { CardWebinarComponent } from 'src/app/components/webinar/card-webinar/card-webinar.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { SharedModule } from 'src/app/shared.module';
let HomeModule = class HomeModule {
};
HomeModule = __decorate([
    NgModule({
        declarations: [HomeComponent, SearchBarComponent],
        imports: [
            CommonModule,
            NgbTypeaheadModule,
            TranslateModule.forChild(),
            RouterModule,
            CardCourseComponent,
            CardWebinarComponent,
            SlickCarouselModule,
            SharedModule
        ],
        exports: [SearchBarComponent]
    })
], HomeModule);
export { HomeModule };
