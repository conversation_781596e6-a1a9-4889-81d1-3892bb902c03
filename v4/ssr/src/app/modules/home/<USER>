var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
import { ViewYoutubeModalComponent } from 'src/app/components/home/<USER>/popup.component';
let HomeComponent = class HomeComponent {
    constructor(authService, route, seoService, webinarService, tutorService, testimonialService, sanitizer, courseService, stateService, appService, modalService) {
        this.authService = authService;
        this.route = route;
        this.seoService = seoService;
        this.webinarService = webinarService;
        this.tutorService = tutorService;
        this.testimonialService = testimonialService;
        this.sanitizer = sanitizer;
        this.courseService = courseService;
        this.stateService = stateService;
        this.appService = appService;
        this.modalService = modalService;
        this.isBrowser = false;
        this.isServer = false;
        this.count = 0;
        this.webinars = [];
        this.currentPage = 1;
        this.pageSize = 4;
        this.searchFields = {};
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.slideConfig = {
            centerMode: false,
            centerPadding: '60px',
            dots: false,
            infinite: true,
            speed: 1000,
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 1000,
            arrows: true,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: true,
                        centerPadding: '40px',
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: true,
                        centerPadding: '40px',
                        slidesToShow: 1,
                        vertical: false,
                        slidesToScroll: 1
                    }
                }
            ]
        };
        this.slideFullConfig = {
            centerMode: false,
            centerPadding: '60px',
            dots: false,
            infinite: true,
            speed: 1000,
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 1000,
            arrows: true,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: true,
                        centerPadding: '40px',
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: true,
                        centerPadding: '40px',
                        slidesToShow: 1,
                        vertical: false,
                        slidesToScroll: 1
                    }
                }
            ]
        };
        this.testimonials = [];
        this.courses = [];
        this.background_how_it_work_block = "url('assets/images/bg-red-pattern.png')";
        this.background_teach_with_us_block = "url('assets/bg-vector.svg')";
        const listCategories = this.route.snapshot.data['categories'];
        this.categories = listCategories.slice(0, 12);
        this.config = this.stateService.getState(STATE.CONFIG);
        const { homeSEO, siteName } = this.config;
        this.seoService.setMetaTitle(siteName);
        this.seoService.setMetaDescription(homeSEO?.description);
        this.seoService.addMetaTags([
            {
                property: 'og:title',
                content: this.config?.siteName
            },
            {
                property: 'og:image',
                content: this.config?.homepagePicture &&
                    this.config?.homepagePicture?.howItWork
                    ? this.config?.homepagePicture?.howItWork
                    : `${environment.url}/assets/images/tutors01.jpg`
            },
            {
                property: 'og:description',
                content: homeSEO?.description || 'Livelearn is online learning platform'
            },
            {
                name: 'keywords',
                content: homeSEO?.keywords
            }
        ]);
    }
    ngOnInit() {
        this.queryParams = this.route.snapshot.queryParams;
        if (this.config) {
            if (this.config.homepagePicture &&
                this.config.homepagePicture.background_how_it_work_block) {
                this.background_how_it_work_block = `url(${this.config.homepagePicture.background_how_it_work_block})`;
            }
            if (this.config.homepagePicture &&
                this.config.homepagePicture.background_teach_with_us_block) {
                this.background_teach_with_us_block = `url(${this.config.homepagePicture.background_teach_with_us_block})`;
            }
        }
        if (this.authService.isLoggedin()) {
            this.authService
                .getCurrentUser()
                .then((resp) => (this.currentUser = resp));
        }
        if (this.appService.isBrowser) {
            this.queryWebinars();
            this.queryTutors();
            this.queryCourse();
            this.queryTestimonial();
        }
    }
    queryWebinars() {
        this.webinarService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            isOpen: true,
            tutorId: this.queryParams.tutorId || '',
            // isAvailable: true,
            disabled: false,
            ...this.searchFields
        })
            .then((resp) => {
            this.webinars = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    queryCourse() {
        this.courseService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            approved: true,
            disabled: false,
            ...this.searchFields
        })
            .then((resp) => {
            this.courses = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    queryTutors() {
        this.tutorService
            .search({
            page: 0,
            take: 10,
            sort: 'createdAt',
            sortType: 'asc',
            isHomePage: true
        })
            .then((resp) => {
            this.count = resp.data.count;
            this.tutors = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    queryTestimonial() {
        this.testimonialService
            .search({ take: 50 })
            .then((resp) => {
            const data = resp.data.items;
            if (data.length) {
                this.testimonials = data.map((item) => Object.assign(item, {
                    urlYoutube: `https://i.ytimg.com/vi/${item.idYoutube}/maxresdefault.jpg`
                }));
            }
        })
            .catch(() => this.appService.toastError());
    }
    selectCategory(category) {
        if (category && this.searchFields.categoryIds !== category._id) {
            this.searchFields.categoryIds = category._id || '';
            this.queryWebinars();
        }
        else if (!category && this.searchFields.categoryIds !== '') {
            this.searchFields.categoryIds = '';
            this.queryWebinars();
        }
        this.categoryName = category.name || '';
    }
    setUrl(idYoutube) {
        return this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.youtube.com/embed/${idYoutube}`);
    }
    viewYoutubeVideo(idYoutube) {
        if (!idYoutube) {
            return;
        }
        const modalRef = this.modalService.open(ViewYoutubeModalComponent, {
            centered: true,
            size: 'lg'
        });
        modalRef.componentInstance['idYoutube'] = idYoutube;
    }
};
HomeComponent = __decorate([
    Component({
        selector: 'app-home',
        templateUrl: './home.component.html',
        styleUrls: ['./home.component.scss']
    })
], HomeComponent);
export { HomeComponent };
