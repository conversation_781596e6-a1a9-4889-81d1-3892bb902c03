var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let PaymentSuccessComponent = class PaymentSuccessComponent {
    constructor(router) {
        this.router = router;
        this.second = 0;
    }
    ngOnInit() {
        this.second = 5;
        this.interval = window.setInterval(() => {
            if (this.second > 0) {
                this.second = this.second - 1;
            }
            else {
                window.clearInterval(this.interval);
                this.router.navigate(['/users/transaction']);
            }
        }, 1000);
    }
    ngOnDestroy() {
        window.clearInterval(this.interval);
    }
};
PaymentSuccessComponent = __decorate([
    Component({
        selector: 'app-payment-success',
        templateUrl: './success.html'
    })
], PaymentSuccessComponent);
export { PaymentSuccessComponent };
