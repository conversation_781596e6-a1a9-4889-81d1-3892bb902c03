var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PaymentSuccessComponent } from './components/success/success.component';
import { PaymentCancelComponent } from './components/cancel/cancel.component';
import { PayComponent } from './components/pay/pay.component';
const routes = [
    {
        path: 'success',
        component: PaymentSuccessComponent
    },
    {
        path: 'cancel',
        component: PaymentCancelComponent
    },
    {
        path: 'pay',
        component: PayComponent,
        resolve: {}
    }
];
let PaymentRoutingModule = class PaymentRoutingModule {
};
PaymentRoutingModule = __decorate([
    NgModule({
        imports: [RouterModule.forChild(routes)],
        exports: [RouterModule]
    })
], PaymentRoutingModule);
export { PaymentRoutingModule };
