var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { PaymentRoutingModule } from '../payment/payment.routing';
import { PaymentSuccessComponent } from './components/success/success.component';
import { PaymentCancelComponent } from './components/cancel/cancel.component';
import { PayComponent } from './components/pay/pay.component';
import { TranslateModule } from '@ngx-translate/core';
import { NgxStripeModule } from 'ngx-stripe';
import { environment } from 'src/environments/environment';
let PaymentModule = class PaymentModule {
};
PaymentModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            NgbModule,
            PaymentRoutingModule,
            NgSelectModule,
            TranslateModule.forChild(),
            NgxStripeModule.forRoot(environment.stripeKey),
            ReactiveFormsModule
        ],
        declarations: [PaymentSuccessComponent, PaymentCancelComponent, PayComponent],
        exports: [],
        entryComponents: []
    })
], PaymentModule);
export { PaymentModule };
