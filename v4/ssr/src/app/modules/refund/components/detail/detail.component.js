var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let DetailRefundRequestComponent = class DetailRefundRequestComponent {
    constructor(router, route, refundService, toasty, stateService) {
        this.router = router;
        this.route = route;
        this.refundService = refundService;
        this.toasty = toasty;
        this.stateService = stateService;
        this.item = {
            _id: '',
            amount: 10,
            reason: '',
            status: '',
            createdAt: ''
        };
        const id = this.route.snapshot.params.id;
        this.config = this.stateService.getState(STATE.CONFIG);
        this.refundService.findOne(id).then((res) => {
            this.item = res.data;
        });
    }
};
DetailRefundRequestComponent = __decorate([
    Component({
        selector: 'app-view-request-refund',
        templateUrl: './detail.html'
    })
], DetailRefundRequestComponent);
export { DetailRefundRequestComponent };
