var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let ListingRequestComponent = class ListingRequestComponent {
    constructor(refundService, appService, seoService, authService, stateService) {
        this.refundService = refundService;
        this.appService = appService;
        this.seoService = seoService;
        this.authService = authService;
        this.stateService = stateService;
        this.items = [];
        this.page = 1;
        this.take = 10;
        this.total = 0;
        this.dateChange = {};
        this.searchFields = {};
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = true;
        this.seoService.setMetaTitle('Refund Request Manager');
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.authService.getCurrentUser().then((resp) => {
            this.userId = resp.id;
            this.query();
        });
    }
    query() {
        this.loading = true;
        this.refundService
            .search({
            page: this.page,
            take: this.take,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            userId: this.userId,
            ...this.searchFields
        })
            .then((resp) => {
            this.items = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.appService.toastError();
            this.loading = false;
        });
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    dateChangeEvent(dateChange) {
        if (!dateChange) {
            this.appService.toastError();
        }
        this.dateChange = dateChange;
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
};
ListingRequestComponent = __decorate([
    Component({
        selector: 'app-request-refund-listing',
        templateUrl: './listing.html'
    })
], ListingRequestComponent);
export { ListingRequestComponent };
