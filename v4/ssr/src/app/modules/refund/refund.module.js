var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { RefundRoutingModule } from './refund.routing';
import { TranslateModule } from '@ngx-translate/core';
import { ListingRequestComponent, DetailRefundRequestComponent } from './components';
import { StatusComponent } from 'src/app/components/uis/status.component';
import { SortComponent } from 'src/app/components/uis/sort/sort.component';
import { SharedModule } from 'src/app/shared.module';
let RefundModule = class RefundModule {
};
RefundModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            RefundRoutingModule,
            NgbModule,
            SharedModule,
            TranslateModule.forChild(),
            forwardRef(() => StatusComponent),
            forwardRef(() => SortComponent)
        ],
        declarations: [ListingRequestComponent, DetailRefundRequestComponent],
        providers: [],
        exports: []
    })
], RefundModule);
export { RefundModule };
