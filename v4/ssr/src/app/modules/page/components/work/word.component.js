var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
let WorkComponent = class WorkComponent {
    constructor(router, seoService, sanitizer, route, stateService) {
        this.router = router;
        this.seoService = seoService;
        this.sanitizer = sanitizer;
        this.route = route;
        this.stateService = stateService;
        this.banner = 'url(' + 'assets/how-it-works/livelearn-bg.svg' + ')';
        this.seoService.setMetaTitle('How it works');
        this.config = this.stateService.getState(STATE.CONFIG);
        this.seoService.addMetaTags([
            {
                property: 'og:title',
                content: 'How does it work?'
            },
            {
                property: 'og:image',
                content: this.config?.homepagePicture &&
                    this.config?.homepagePicture?.howItWork
                    ? this.config?.homepagePicture?.howItWork
                    : `${environment.url}/assets/images/tutors01.jpg`
            },
            {
                property: 'og:description',
                content: 'We provide three types of learning modes: Self-paced courses, Live 1-on-classes, Live group webinars.'
            },
            {
                name: 'keywords',
                content: 'How does it work?'
            }
        ]);
    }
    ngOnInit() {
        if (Object.keys(this.config).length > 0) {
            this.iframe = this.setUrl(this.config.youtubeHowItWork);
            if (this.config.teachWithUsPicture &&
                this.config.teachWithUsPicture.banner) {
                this.banner = `url(${this.config.teachWithUsPicture.banner})`;
            }
        }
    }
    setUrl(urlYoutubeHowItWork) {
        return this.sanitizer.bypassSecurityTrustHtml(urlYoutubeHowItWork);
    }
};
WorkComponent = __decorate([
    Component({
        selector: 'app-work',
        templateUrl: './word.html'
    })
], WorkComponent);
export { WorkComponent };
