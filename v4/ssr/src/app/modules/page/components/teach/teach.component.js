var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
import * as jQuery from 'jquery';
let TeachWithUsComponent = class TeachWithUsComponent {
    constructor(router, tutorService, authService, seoService, stateService, appService) {
        this.router = router;
        this.tutorService = tutorService;
        this.authService = authService;
        this.seoService = seoService;
        this.stateService = stateService;
        this.appService = appService;
        this.slideConfig = {
            centerMode: false,
            centerPadding: '60px',
            dots: false,
            infinite: true,
            speed: 2000,
            slidesToShow: 3,
            slidesToScroll: 2,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: false,
                        centerPadding: '40px',
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: false,
                        centerPadding: '40px',
                        slidesToShow: 1,
                        vertical: false,
                        slidesToScroll: 1
                    }
                }
            ]
        };
        this.banner = 'url(' + 'assets/how-it-works/livelearn-bg.svg' + ')';
        this.banner_sm = 'url(' + 'assets/images/teach-with-us-banner.png' + ')';
        this.seoService.setMetaTitle('Teach with us');
        this.config = this.stateService.getState(STATE.CONFIG);
        this.seoService.addMetaTags([
            {
                property: 'og:title',
                content: 'Make a global impact'
            },
            {
                property: 'og:image',
                content: this.config?.teachWithUsPicture &&
                    this.config?.teachWithUsPicture?.banner
                    ? this.config?.teachWithUsPicture?.banner
                    : `${environment.url}/assets/images/tutors01.jpg`
            },
            {
                property: 'og:description',
                content: 'Register as a tutor with us and earn money creating online courses, taking live classes, and hosting webinars for learners around the world.'
            },
            {
                name: 'keywords',
                content: 'Become a Tutor'
            }
        ]);
    }
    ngOnInit() {
        if (Object.keys(this.config).length > 0) {
            if (this.config.teachWithUsPicture &&
                this.config.teachWithUsPicture.banner) {
                this.banner = `url(${this.config.teachWithUsPicture.banner})`;
            }
            if (this.config.teachWithUsPicture &&
                this.config.teachWithUsPicture.banner_sm) {
                this.banner_sm = this.config.teachWithUsPicture.banner_sm;
            }
        }
        (function ($) {
            $(document).ready(function () {
                $('.counter').each(function () {
                    const $this = $(this);
                    const countTo = $this.attr('data-count');
                    $({ countNum: $this.text() }).animate({
                        countNum: countTo
                    }, {
                        duration: 8000,
                        easing: 'linear',
                        step: function () {
                            $this.text(Math.floor(Number(this.countNum)));
                        },
                        complete: function () {
                            $this.text(this.countNum);
                        }
                    });
                });
            });
        })(jQuery);
        this.queryTutors();
    }
    queryTutors() {
        this.tutorService
            .search({
            page: 0,
            take: 10,
            sort: 'createdAt',
            sortType: 'asc',
            isHomePage: true
        })
            .then((resp) => {
            this.tutors = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    becomeInstructor() {
        this.authService.removeToken();
        this.router.navigate(['/auth/sign-up']);
    }
};
TeachWithUsComponent = __decorate([
    Component({
        selector: 'app-teach-with-us',
        templateUrl: './teach.html'
    })
], TeachWithUsComponent);
export { TeachWithUsComponent };
