var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let StaticPageComponent = class StaticPageComponent {
    constructor(router, route, pageService, seoService) {
        this.router = router;
        this.route = route;
        this.pageService = pageService;
        this.seoService = seoService;
        this.submitted = false;
        this.route.params.subscribe((params) => {
            this.alias = params.alias;
            this.pageService.findOne(this.alias).then((resp) => {
                this.page = resp.data;
                this.seoService.setMetaTitle(this.page.title);
            });
        });
    }
    ngOnInit() { }
};
StaticPageComponent = __decorate([
    Component({
        selector: 'app-static-page',
        templateUrl: './page.component.html'
    })
], StaticPageComponent);
export { StaticPageComponent };
