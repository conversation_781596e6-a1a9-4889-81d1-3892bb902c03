var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { WorkComponent } from './components/work/word.component';
import { TeachWithUsComponent } from './components/teach/teach.component';
import { PageErrorComponent } from './components/page-error/page-error.component';
import { StaticPageComponent } from './components/static-page/page.component';
const routes = [
    {
        path: 'how-does-it-work',
        component: WorkComponent
    },
    {
        path: 'teach-with-us',
        component: TeachWithUsComponent,
        resolve: {}
    },
    {
        path: 'error/:code',
        component: PageErrorComponent
    },
    {
        path: ':alias',
        component: StaticPageComponent
    }
];
let PageRoutingModule = class PageRoutingModule {
};
PageRoutingModule = __decorate([
    NgModule({
        imports: [RouterModule.forChild(routes)],
        exports: [RouterModule]
    })
], PageRoutingModule);
export { PageRoutingModule };
