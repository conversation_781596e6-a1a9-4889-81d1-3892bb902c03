var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PageRoutingModule } from './page.routing';
import { WorkComponent } from './components/work/word.component';
import { TeachWithUsComponent } from './components/teach/teach.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { PageErrorComponent } from './components/page-error/page-error.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';
import { StaticPageService } from 'src/app/services';
import { StaticPageComponent } from './components/static-page/page.component';
let PageModule = class PageModule {
};
PageModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            NgbModule,
            PageRoutingModule,
            SlickCarouselModule,
            TranslateModule.forChild(),
            forwardRef(() => SharedModule)
        ],
        declarations: [
            WorkComponent,
            TeachWithUsComponent,
            PageErrorComponent,
            StaticPageComponent
        ],
        exports: [],
        entryComponents: [],
        providers: [StaticPageService]
    })
], PageModule);
export { PageModule };
