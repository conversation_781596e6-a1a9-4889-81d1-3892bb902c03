var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let ForgotComponent = class ForgotComponent {
    constructor(auth, router, stateService, appService) {
        this.router = router;
        this.stateService = stateService;
        this.appService = appService;
        this.credentials = {
            email: ''
        };
        this.submitted = false;
        this.Auth = auth;
        this.appConfig = this.stateService.getState(STATE.CONFIG);
    }
    login(frm) {
        this.submitted = true;
        if (frm.invalid) {
            return;
        }
        this.Auth.forgot(this.credentials.email.toLowerCase())
            .then(() => {
            this.appService.toastSuccess('An email was sent to your address');
            this.router.navigate(['/auth/login']);
        })
            .catch((e) => this.appService.toastError(e));
    }
    ngAfterViewInit() {
        const target = document.getElementById('email-input');
        target.addEventListener('paste', (event) => {
            event.preventDefault();
            const clipboard = event.clipboardData, text = clipboard.getData('Text');
            event.target.value = text.trim();
            this.credentials.email = text.trim();
        }, false);
    }
};
ForgotComponent = __decorate([
    Component({
        templateUrl: 'forgot.component.html'
    })
], ForgotComponent);
export { ForgotComponent };
