var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { StateService } from 'src/app/services';
import { isPlatformBrowser } from '@angular/common';
let LoginComponent = class LoginComponent {
    constructor(auth, router, route, seoService, store, platformId, appService) {
        this.router = router;
        this.route = route;
        this.seoService = seoService;
        this.store = store;
        this.platformId = platformId;
        this.appService = appService;
        this.credentials = {
            email: '',
            password: '',
            rememberMe: false
        };
        this.submitted = false;
        this.returnUrl = '';
        this.Auth = auth;
        this.appConfig = this.store.getState('config');
        this.seoService.setMetaTitle('Login');
    }
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            let currentUrl = localStorage.getItem('currentUrl');
            if (currentUrl && currentUrl === '/auth/sign-up')
                currentUrl = '/home';
            this.returnUrl =
                this.route.snapshot.queryParams['returnUrl'] ||
                    currentUrl ||
                    '/users/dashboard';
            if (this.Auth.getAccessToken()) {
                this.router.navigate(['/']);
            }
        }
    }
    login(frm) {
        this.submitted = true;
        if (frm.invalid) {
            return;
        }
        this.credentials.email = this.credentials.email
            .toLowerCase()
            .replace(/\s+/g, '');
        this.Auth.login(this.credentials)
            .then((resp) => {
            if (resp && resp._id) {
                this.router.navigateByUrl(this.returnUrl, {
                    state: {
                        current: resp
                    }
                });
            }
        })
            .catch((err) => {
            if (err) {
                return this.appService.toastError(err);
            }
            return this.appService.toastError();
        });
    }
    ngAfterViewInit() {
        if (isPlatformBrowser(this.platformId)) {
            const target = document.getElementById('email-input');
            if (target) {
                target.addEventListener('paste', (event) => {
                    event.preventDefault();
                    const clipboard = event.clipboardData, text = clipboard.getData('Text');
                    event.target.value = text.trim();
                    this.credentials.email = text.trim();
                }, false);
            }
        }
    }
};
LoginComponent = __decorate([
    Component({
        templateUrl: 'login.component.html'
    }),
    __param(4, Inject(StateService)),
    __param(5, Inject(PLATFORM_ID))
], LoginComponent);
export { LoginComponent };
