var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
let SignupComponent = class SignupComponent {
    constructor(auth, router, seoService, appService, stateService) {
        this.auth = auth;
        this.router = router;
        this.seoService = seoService;
        this.appService = appService;
        this.stateService = stateService;
        this.account = {
            email: '',
            password: '',
            name: '',
            type: '',
            timezone: '',
            dateOfBirth: '',
            parentName: '',
            parentEmail: ''
        };
        this.accountTutor = {
            email: '',
            password: '',
            name: '',
            issueDocument: '',
            resumeDocument: null,
            certificationDocument: null,
            timezone: '',
            introVideoId: null,
            introYoutubeId: null
        };
        this.introVideoType = 'upload';
        this.confirm = {
            pw: ''
        };
        this.isPasswordMatched = false;
        this.submitted = false;
        this.idDocumentOptions = {};
        this.resumeOptions = {};
        this.certificationOptions = {};
        this.introVideoOptions = {};
        this.loading = false;
        this.isAgreeWithTerms = true;
        this.showParentalFields = false;
        this.maxFileSize = environment.maximumFileSize;
        this.appConfig = this.stateService.getState(STATE.CONFIG);
        if (this.appConfig) {
            const title = this.appConfig.siteName + ' - Sign Up';
            this.seoService.setMetaTitle(title);
        }
        this.introVideoOptions = {
            url: environment.apiBaseUrl + '/tutors/upload-introVideo',
            onCompleteItem: (resp) => {
                this.accountTutor.introVideoId = resp.data._id;
                this.loading = false;
            },
            onFileSelect: (resp) => {
                const lastIndex = resp.length - 1;
                const file = resp[lastIndex].file;
                const ext = file.name.split('.').pop().toLowerCase();
                if (['mp4', 'webm', '3gp', 'ogg', 'wmv', 'webm'].indexOf(ext) === -1) {
                    this.introVideoOptions.uploader.clearQueue();
                    return this.appService.toastError('Invalid file type');
                }
                this.introVideo = file;
            },
            uploadOnSelect: true,
            id: 'id-introVideo',
            onUploading: () => (this.loading = true)
        };
        this.idDocumentOptions = {
            url: environment.apiBaseUrl + '/tutors/upload-document',
            onCompleteItem: (resp) => {
                this.accountTutor.issueDocument = resp.data._id;
                this.loading = false;
            },
            onFileSelect: (resp) => {
                const lastIndex = resp.length - 1;
                const file = resp[lastIndex].file;
                const ext = file.name.split('.').pop().toLowerCase();
                if (['pdf', 'doc', 'docx', 'zip', 'rar', 'jpg', 'jpeg', 'png'].indexOf(ext) === -1) {
                    this.idDocumentOptions.uploader.clearQueue();
                    return this.appService.toastError('Invalid file type');
                }
                this.idDocumentFile = file;
            },
            uploadOnSelect: true,
            id: 'id-document',
            onUploading: () => (this.loading = true)
        };
        this.resumeOptions = {
            url: environment.apiBaseUrl + '/tutors/upload-document',
            onCompleteItem: (resp) => {
                this.accountTutor.resumeDocument = resp.data._id;
                this.loading = false;
            },
            onFileSelect: (resp) => {
                const lastIndex = resp.length - 1;
                const file = resp[lastIndex].file;
                const ext = file.name.split('.').pop().toLowerCase();
                if (['pdf'].indexOf(ext) === -1) {
                    this.resumeOptions.uploader.clearQueue();
                    return this.appService.toastError('Invalid file type');
                }
                this.resumeFile = file;
            },
            uploadOnSelect: true,
            id: 'id-resume',
            onUploading: () => (this.loading = true)
        };
        this.certificationOptions = {
            url: environment.apiBaseUrl + '/tutors/upload-document',
            onCompleteItem: (resp) => {
                this.accountTutor.certificationDocument = resp.data._id;
                this.loading = false;
            },
            onFileSelect: (resp) => {
                const lastIndex = resp.length - 1;
                const file = resp[lastIndex].file;
                const ext = file.name.split('.').pop().toLowerCase();
                if (['pdf'].indexOf(ext) === -1) {
                    this.certificationOptions.uploader.clearQueue();
                    return this.appService.toastError('Invalid file type');
                }
                this.certificationFile = file;
            },
            uploadOnSelect: true,
            id: 'id-verification',
            onUploading: () => (this.loading = true)
        };
    }
    onlyNumberKey(event) {
        return event.charCode === 8 || event.charCode === 0
            ? null
            : event.charCode >= 48 && event.charCode <= 57;
    }
    onDateOfBirthChange() {
        if (this.account.type === 'student' && this.account.dateOfBirth) {
            const birthDate = new Date(this.account.dateOfBirth);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            // Calculate exact age
            const exactAge = monthDiff < 0 ||
                (monthDiff === 0 && today.getDate() < birthDate.getDate())
                ? age - 1
                : age;
            this.showParentalFields = exactAge < 13;
            // Clear parental fields if not needed
            if (!this.showParentalFields) {
                this.account.parentName = '';
                this.account.parentEmail = '';
            }
        }
        else {
            this.showParentalFields = false;
            this.account.parentName = '';
            this.account.parentEmail = '';
        }
    }
    async submit(frm) {
        this.submitted = true;
        if (frm.invalid) {
            return;
        }
        if (!this.account.timezone) {
            return this.appService.toastError('Please select timezone');
        }
        if (this.account.password !== this.confirm.pw) {
            this.isPasswordMatched = true;
            return this.appService.toastError('Confirm password and password dont match');
        }
        if (this.account.type === '') {
            return this.appService.toastError('Please select type');
        }
        // Validate parental information for students under 13
        if (this.showParentalFields) {
            if (!this.account.parentName || this.account.parentName.trim() === '') {
                return this.appService.toastError('Parent name is required for students under 13 years old');
            }
            if (!this.account.parentEmail || this.account.parentEmail.trim() === '') {
                return this.appService.toastError('Parent email is required for students under 13 years old');
            }
        }
        this.account.email = this.account.email.toLowerCase();
        if (this.account.type === 'tutor') {
            this.accountTutor.name = this.account.name;
            this.accountTutor.email = this.account.email;
            this.accountTutor.password = this.account.password;
            this.accountTutor.timezone = this.account.timezone;
            if (this.introVideoType === 'youtube') {
                this.accountTutor.introVideoId = null;
            }
            // Clean up empty optional fields
            if (!this.accountTutor.resumeDocument ||
                this.accountTutor.resumeDocument === '') {
                this.accountTutor.resumeDocument = null;
            }
            if (!this.accountTutor.certificationDocument ||
                this.accountTutor.certificationDocument === '') {
                this.accountTutor.certificationDocument = null;
            }
            if (!this.accountTutor.introVideoId ||
                this.accountTutor.introVideoId === '') {
                this.accountTutor.introVideoId = null;
            }
            if (!this.accountTutor.introYoutubeId ||
                this.accountTutor.introYoutubeId === '') {
                this.accountTutor.introYoutubeId = null;
            }
            if (!this.accountTutor.issueDocument) {
                return this.appService.toastError('Please upload verification document');
            }
            return this.auth
                .registerTutor(this.accountTutor)
                .then(() => {
                this.appService.toastSuccess('Your account has been created, please verify your email then login');
                this.router.navigate(['/auth/login']);
            })
                .catch((err) => {
                this.appService.toastError(err);
            });
        }
        this.auth
            .register(this.account)
            .then(() => {
            this.appService.toastSuccess('Your account has been created, please verify your email then login');
            this.router.navigate(['/auth/login']);
        })
            .catch((err) => {
            console.log(err);
            this.appService.toastError(err);
        });
    }
    changeTimezone(event) {
        if (event === 'Asia/Saigon') {
            this.account.timezone = 'Asia/Ho_Chi_Minh';
        }
        else {
            this.account.timezone = event;
        }
    }
    changeUploadType(event) {
        if (event.target.value === 'youtube') {
            this.accountTutor.introYoutubeId = null;
        }
        else {
            this.accountTutor.introYoutubeId = null;
        }
    }
    ngAfterViewInit() {
        const target = document.getElementById('email-input');
        target.addEventListener('paste', (event) => {
            event.preventDefault();
            const clipboard = event.clipboardData, text = clipboard.getData('Text');
            event.target.value = text.trim();
            this.account.email = text.trim();
        }, false);
    }
};
SignupComponent = __decorate([
    Component({
        templateUrl: 'signup.component.html'
    })
], SignupComponent);
export { SignupComponent };
