var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './login/login.component';
import { AuthRoutingModule } from './auth.routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SignupComponent } from './register/signup.component';
import { TimezoneComponent } from 'src/app/components/uis/timezone.component';
import { FileUploadComponent } from 'src/app/components/media/file-upload/file-upload.component';
import { ForgotComponent } from './forgot/forgot.component';
let AuthModule = class AuthModule {
};
AuthModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            AuthRoutingModule,
            FormsModule,
            ReactiveFormsModule,
            TranslateModule.forChild(),
            forwardRef(() => TimezoneComponent),
            forwardRef(() => FileUploadComponent)
        ],
        exports: [LoginComponent, SignupComponent],
        declarations: [LoginComponent, SignupComponent, ForgotComponent],
        providers: []
    })
], AuthModule);
export { AuthModule };
