var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';
import { TutorListComponent } from './list/tutor-list.component';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { TutorCardComponent } from 'src/app/components/tutor/tutor-card/tutor-card.component';
import { CertificateComponent } from 'src/app/components/tutor/certificate/certificate.component';
import { CardTextComponent } from 'src/app/components/uis/card-text.component';
import { TutorProfileComponent } from './details/profile.component';
import { ReviewListComponent } from 'src/app/components/review/review-list/list.component';
import { ShareIconsModule } from 'ngx-sharebuttons/icons';
import { ShareButtonModule } from 'ngx-sharebuttons/button';
import { TutorRoutingModule } from './tutor.routing';
import { ConfirmModalComponent } from 'src/app/components/booking/confirm/confirm.component';
import { BookingComponent } from './booking/booking.component';
import { ApplyCouponComponent } from 'src/app/components/coupon/apply-coupon/apply.component';
import { UserAvailableTimeComponent } from 'src/app/components/calendar/tutor-available-time/tutor-available-time.component';
let TutorModule = class TutorModule {
};
TutorModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            TranslateModule.forChild(),
            SharedModule,
            NgbPaginationModule,
            FormsModule,
            CertificateComponent,
            CardTextComponent,
            ReviewListComponent,
            ShareIconsModule,
            ShareButtonModule,
            TutorRoutingModule,
            ApplyCouponComponent,
            UserAvailableTimeComponent
        ],
        declarations: [
            TutorListComponent,
            TutorCardComponent,
            TutorProfileComponent,
            ConfirmModalComponent,
            BookingComponent
        ],
        exports: []
    })
], TutorModule);
export { TutorModule };
