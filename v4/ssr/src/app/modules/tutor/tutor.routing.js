var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TutorListComponent } from './list/tutor-list.component';
import categoriesResolver from 'src/app/services/resolvers/category.resolver';
import { TutorProfileComponent } from './details/profile.component';
import tutorDetailResolver from 'src/app/services/resolvers/tutor-detail.resolver';
import { BookingComponent } from './booking/booking.component';
const routes = [
    {
        path: '',
        component: TutorListComponent,
        resolve: {
            // search: TutorSearchResolver,
            categories: categoriesResolver
        }
    },
    {
        path: ':username',
        component: TutorProfileComponent,
        resolve: {
            tutor: tutorDetailResolver
        }
    },
    {
        path: ':username/booking',
        component: BookingComponent,
        resolve: {
            tutor: tutorDetailResolver
        }
    }
];
let TutorRoutingModule = class TutorRoutingModule {
};
TutorRoutingModule = __decorate([
    NgModule({
        imports: [RouterModule.forChild(routes)],
        exports: [RouterModule]
    })
], TutorRoutingModule);
export { TutorRoutingModule };
