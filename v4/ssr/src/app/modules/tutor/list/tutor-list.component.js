var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, HostListener } from '@angular/core';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { STATE } from 'src/app/services';
const time_format = 'HH:mm:ss';
let TutorListComponent = class TutorListComponent {
    constructor(route, seoService, tutorService, router, gradeService, countryService, subjectService, topicService, translate, calendarService, stateService, appService) {
        this.route = route;
        this.seoService = seoService;
        this.tutorService = tutorService;
        this.router = router;
        this.gradeService = gradeService;
        this.countryService = countryService;
        this.subjectService = subjectService;
        this.topicService = topicService;
        this.translate = translate;
        this.calendarService = calendarService;
        this.stateService = stateService;
        this.appService = appService;
        this.page = 1;
        this.pageSize = 12;
        this.tutors = [];
        this.subjects = [];
        this.total = 0;
        this.sort = '';
        this.sortType = '';
        this.showMoreFilter = false;
        this.searchFields = {
            subjectIds: '',
            grade: '',
            categoryIds: '',
            countryCode: '',
            topicIds: ''
        };
        this.grades = [];
        this.loading = false;
        this.categories = [];
        this.dateChange = {};
        this.topics = [];
        this._schedule$ = new Subject();
        this.scheduleByTutor = [];
        this.loadingSchedule = false;
        this.calendar = {};
        this.valid_time = {
            morning: {
                start: moment('06:00:00', time_format),
                to: moment('12:00:00', time_format)
            },
            afternoon: {
                start: moment('12:00:00', time_format),
                to: moment('18:00:00', time_format)
            },
            evening: {
                start: moment('18:00:00', time_format),
                to: moment('24:00:00', time_format)
            },
            night: {
                start: moment('00:00:00', time_format),
                to: moment('06:00:00', time_format)
            }
        };
        this.seoService.setMetaTitle('List Tutor');
        this.seoService.setMetaDescription("If you're looking for someone to help make calculus sound sensical , you've come to the right place.Below you'll find some of our top calculus tutors.");
        const data = this.route.snapshot.data['search'];
        if (data) {
            this.tutors = data.items;
            this.total = data.count;
        }
        this.categories = this.route.snapshot.data['categories'];
        this.route.queryParams.subscribe((params) => {
            let filter = { ...params };
            if (params.category) {
                const category = this.categories.find((item) => item.alias === params.category);
                filter = {
                    categoryIds: category ? category._id : ''
                };
            }
            this.searchFields = { ...this.searchFields, ...filter };
            if (this.searchFields.categoryIds)
                this.querySubjects();
            this.page = params.page ? parseInt(params.page, 10) : 1;
            this.query();
        });
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    scrollHandler() {
        if (this.activeTutor && !this.isHoverTutor) {
            this.activeTutor = null;
        }
    }
    onHoverTutor({ top, tutor }) {
        this.isHoverTutor = !!tutor;
        if (tutor && (!this.activeTutor || tutor._id !== this.activeTutor?._id)) {
            this.activeTutor = tutor;
            $('.floatind_videobx').css({ top: `${top}px` });
            this.loadingSchedule = true;
            this.calendarService
                .all({
                tutorId: this.activeTutor._id,
                startTime: moment().startOf('week').toDate().toISOString(),
                toTime: moment().endOf('week').toDate().toISOString(),
                take: 10000,
                type: 'subject',
                sort: 'startTime',
                sortType: 'asc'
            })
                .then((resp) => {
                if (resp.data && resp.data.items) {
                    this.scheduleByTutor = resp.data.items;
                    this.mappingDataInTime(resp.data.items);
                }
                this.loadingSchedule = false;
            });
        }
    }
    ngOnInit() {
        this.countries = this.countryService.getCountry();
        this.gradeService
            .search({
            take: 100,
            sort: 'ordering',
            sortType: 'asc'
        })
            .then((resp) => {
            this.grades = resp.data.items;
        });
    }
    showMore() {
        this.showMoreFilter = !this.showMoreFilter;
    }
    query() {
        if (!this.loading) {
            this.loading = true;
            this.tutorService
                .search({
                page: this.page,
                take: this.pageSize,
                sort: this.sort,
                sortType: this.sortType,
                ...this.searchFields,
                ...this.dateChange
            })
                .then((resp) => {
                this.total = resp.data.count;
                this.tutors = resp.data.items;
                if (this.tutors.length) {
                    this.activeTutor = this.tutors[0];
                    this.loadingSchedule = true;
                    this.calendarService
                        .all({
                        tutorId: this.activeTutor._id,
                        startTime: moment().startOf('week').toDate().toISOString(),
                        toTime: moment().endOf('week').toDate().toISOString(),
                        take: 10000,
                        type: 'subject',
                        sort: 'startTime',
                        sortType: 'asc'
                    })
                        .then((res) => {
                        if (res.data && res.data.items) {
                            this.scheduleByTutor = res.data.items;
                            this.mappingDataInTime(res.data.items);
                            this.loadingSchedule = false;
                        }
                    });
                }
                else {
                    this.activeTutor = null;
                }
                this.loading = false;
            })
                .catch(() => {
                this.loading = false;
                this.appService.toastError(null);
            });
        }
    }
    apply() {
        this.showMore();
        this.query();
    }
    gradeChange() {
        this.page = 1;
        this.router.navigate(['/tutors'], {
            queryParams: {
                subjectId: this.searchFields.subjectId,
                grade: this.searchFields.grade,
                countryCode: this.searchFields.countryCode
            }
        });
        this.query();
    }
    subjectChange() {
        this.page = 1;
        // this.query();
        this.router.navigate(['/tutors'], {
            queryParams: {
                subjectId: this.searchFields.subjectId,
                grade: this.searchFields.grade,
                countryCode: this.searchFields.countryCode
            }
        });
        this.query();
    }
    dateChangeEvent(dateChange) {
        if (!dateChange) {
            if (this.dateChange.startTime && this.dateChange.toTime) {
                delete this.dateChange.startTime;
                delete this.dateChange.toTime;
                this.query();
            }
        }
        else {
            this.dateChange = {
                startTime: dateChange.from,
                toTime: dateChange.to
            };
            this.query();
        }
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.router.navigate([], {
            queryParams: { page: this.page },
            queryParamsHandling: 'merge'
        });
    }
    selectCategory() {
        if (this.searchFields.categoryIds) {
            this.querySubjects();
            this.searchFields.topicIds = [];
        }
        else {
            this.searchFields.subjectIds = [];
            this.searchFields.topicIds = [];
            this.subjects = [];
            this.topics = [];
        }
        this.query();
    }
    querySubjects() {
        this.subjectService
            .search({
            categoryIds: this.searchFields.categoryIds,
            take: 1000,
            isActive: true
        })
            .then((resp) => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.subjects = resp.data.items;
            }
            else {
                this.subjects = [];
            }
        });
    }
    selectSubject() {
        if (this.searchFields.subjectIds) {
            this.queryTopic();
        }
        else {
            this.searchFields.topicIds = [];
            this.topics = [];
        }
        this.query();
    }
    queryTopic() {
        this.topicService
            .search({
            subjectIds: this.searchFields.subjectIds,
            take: 1000,
            isActive: true
        })
            .then((resp) => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.topics = resp.data.items;
            }
            else {
                this.topics = [];
            }
        });
    }
    mappingDataInTime(items) {
        this.calendar = {
            Sunday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Monday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Tuesday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Wednesday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Thursday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Friday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            },
            Saturday: {
                morning: false,
                afternoon: false,
                evening: false,
                night: false
            }
        };
        if (items.length !== 0) {
            items.map((item) => {
                const item_day = moment(item.startTime).format('dddd');
                const item_time = moment(moment(item.startTime).format(time_format), time_format);
                if (item_time.isBetween(this.valid_time.morning.start, this.valid_time.morning.to, null, '[)')) {
                    this.calendar[item_day]['morning'] = true;
                }
                else if (item_time.isBetween(this.valid_time.afternoon.start, this.valid_time.afternoon.to, null, '[)')) {
                    this.calendar[item_day]['afternoon'] = true;
                }
                else if (item_time.isBetween(this.valid_time.evening.start, this.valid_time.evening.to, null, '[)')) {
                    this.calendar[item_day]['evening'] = true;
                }
                else if (item_time.isBetween(this.valid_time.night.start, this.valid_time.night.to, null, '[)')) {
                    this.calendar[item_day]['night'] = true;
                }
            });
        }
    }
};
__decorate([
    HostListener('window:scroll', ['$event'])
], TutorListComponent.prototype, "scrollHandler", null);
TutorListComponent = __decorate([
    Component({
        selector: 'app-tutor-list',
        templateUrl: './tutor-list.component.html'
        // styleUrls: ['./tutor-list.component.scss'],
    })
], TutorListComponent);
export { TutorListComponent };
