var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { STATE } from 'src/app/services';
import { environment } from 'src/environments/environment';
import * as jQuery from 'jquery';
import { isPlatformBrowser } from '@angular/common';
let TutorProfileComponent = class TutorProfileComponent {
    constructor(route, appService, seoService, authService, languageService, webinarService, sanitizer, tutorFavoriteService, courseService, router, stateService, platformId) {
        this.route = route;
        this.appService = appService;
        this.seoService = seoService;
        this.authService = authService;
        this.languageService = languageService;
        this.webinarService = webinarService;
        this.sanitizer = sanitizer;
        this.tutorFavoriteService = tutorFavoriteService;
        this.courseService = courseService;
        this.router = router;
        this.stateService = stateService;
        this.platformId = platformId;
        this.languageNames = [];
        this.objectLanguage = {};
        this.gradeNames = [];
        this.webinars = [];
        this.courses = [];
        this.currentPage = 1;
        this.pageSize = 5;
        this.searchFields = {};
        this.isLoggedin = false;
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.count = 0;
        this.countCourse = 0;
        this.showChar = 500;
        this.showMore = false;
        this.slideConfig = {
            centerMode: false,
            centerPadding: '60px',
            dots: false,
            infinite: true,
            speed: 2000,
            slidesToShow: 3,
            slidesToScroll: 2,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: false,
                        centerPadding: '40px',
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        arrows: false,
                        centerMode: false,
                        dots: false,
                        centerPadding: '40px',
                        slidesToShow: 1,
                        vertical: false,
                        slidesToScroll: 1
                    }
                }
            ]
        };
        this.statsReview = {
            ratingAvg: 0,
            ratingScore: 0,
            totalRating: 0
        };
        this.optionsReview = {
            rateTo: ''
        };
        // tslint:disable-next-line:max-line-length
        this.moreTag = '';
        this.webinarOptions = {
            webinars: [],
            currentPage: 1,
            pageSize: 6,
            sortOption: {
                sortBy: 'createdAt',
                sortType: 'asc'
            },
            count: 0
        };
        this.courseOptions = {
            courses: [],
            currentPage: 1,
            pageSize: 6,
            sortOption: {
                sortBy: 'createdAt',
                sortType: 'asc'
            },
            count: 0
        };
        this.tutor = this.route.snapshot.data['tutor'];
        this.seoService.setMetaTitle(this.tutor.name);
        this.config = this.stateService.getState(STATE.CONFIG);
        this.webUrl = environment.url;
        this.seoService.addMetaTags([
            {
                property: 'og:title',
                content: this.tutor.name
            },
            {
                property: 'og:image',
                content: this.tutor.avatarUrl
            },
            {
                property: 'og:description',
                content: 'Livelearn is online learning platform'
            }
        ]);
    }
    ngOnInit() {
        this.languages = this.languageService.getLang();
        this.objectLanguage = this.languageService.languages;
        this.isLoggedin = this.authService.isLoggedin();
        this.optionsReview.rateTo = this.tutor._id;
        this.statsReview = {
            ...this.statsReview,
            ...{
                ratingAvg: this.tutor.ratingAvg,
                totalRating: this.tutor.totalRating,
                ratingScore: this.tutor.ratingScore
            }
        };
        if (this.tutor.languages) {
            this.mapLanguageName(this.tutor.languages);
        }
        if (this.tutor.gradeItems && this.tutor.gradeItems.length > 0) {
            this.mapGradeName(this.tutor.gradeItems);
        }
        this.queryWebinar();
        this.queryCourse();
        if (this.tutor && this.tutor.bio && this.tutor.bio.length > this.showChar) {
            this.showMore = true;
        }
        this.urlYoutube = this.setUrl(this.tutor.idYoutube);
    }
    queryWebinar() {
        this.webinarService
            .search({
            page: this.webinarOptions.currentPage,
            take: this.webinarOptions.pageSize,
            sort: `${this.webinarOptions.sortOption.sortBy}`,
            sortType: `${this.webinarOptions.sortOption.sortType}`,
            isOpen: true,
            tutorId: this.tutor._id || '',
            disabled: false,
            ...this.searchFields
        })
            .then((resp) => {
            this.webinarOptions.count = resp.data.count;
            this.webinarOptions.webinars = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    queryCourse() {
        this.courseService
            .search({
            page: this.courseOptions.currentPage,
            take: this.courseOptions.pageSize,
            sort: `${this.courseOptions.sortOption.sortBy}`,
            sortType: `${this.courseOptions.sortOption.sortType}`,
            isOpen: true,
            tutorId: this.tutor._id || '',
            approved: true,
            disabled: false,
            ...this.searchFields
        })
            .then((resp) => {
            this.courseOptions.count = resp.data.count;
            this.courseOptions.courses = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    mapGradeName(gradeItems) {
        gradeItems.forEach((grade) => {
            this.gradeNames.push(grade?.name);
        });
    }
    mapLanguageName(languageKeys) {
        languageKeys.forEach((lang) => {
            this.languageNames.push(this.objectLanguage[lang]);
        });
    }
    setUrl(idYoutube) {
        return this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.youtube.com/embed/${idYoutube}`);
    }
    favorite() {
        if (!this.isLoggedin)
            this.appService.toastError('Please Log in to add to your favorites');
        else {
            this.tutorFavoriteService
                .favorite({
                tutorId: this.tutor._id,
                type: 'tutor'
            }, 'tutor')
                .then(() => {
                this.tutor.isFavorite = true;
                this.appService.toastSuccess('Added to your favorite tutor list successfully!');
            })
                .catch(() => this.appService.toastError());
        }
    }
    unFavorite() {
        if (!this.isLoggedin)
            this.appService.toastError('Please loggin to use this feature!');
        else {
            this.tutorFavoriteService
                .unFavorite(this.tutor._id, 'tutor')
                .then(() => {
                this.tutor.isFavorite = false;
                this.appService.toastSuccess('Removed from your favorite tutor list successfully');
            })
                .catch(() => this.appService.toastError());
        }
    }
    clickCategory(catId) {
        const categoryIds = [];
        categoryIds.push(catId);
        this.searchFields.categoryIds = categoryIds.join(',');
        this.router.navigate(['/categories'], {
            queryParams: { categoryIds: this.searchFields.categoryIds }
        });
    }
    ngAfterViewInit() {
        if (isPlatformBrowser(this.platformId)) {
            (function ($) {
                $(document).ready(function () {
                    $('#btn-view-webinars').click(function () {
                        $('html, body').animate({
                            scrollTop: $('#view-webinars').offset().top
                        }, 300);
                    });
                    $('#btn-view-courses').click(function () {
                        $('html, body').animate({
                            scrollTop: $('#view-courses').offset().top
                        }, 300);
                    });
                });
            })(jQuery);
        }
    }
    getCategories(course) {
        let categories = '';
        if (course.categories.length > 0) {
            course.categories.forEach((cat) => {
                categories = categories + cat.name + ', ';
            });
            categories = categories.slice(0, -2);
        }
        return categories;
    }
};
TutorProfileComponent = __decorate([
    Component({
        templateUrl: './profile.html'
    }),
    __param(11, Inject(PLATFORM_ID))
], TutorProfileComponent);
export { TutorProfileComponent };
