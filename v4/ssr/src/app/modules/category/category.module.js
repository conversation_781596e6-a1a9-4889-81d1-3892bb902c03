var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { WebinarModule } from '../webinar/webinar.module';
import { NgxStripeModule } from 'ngx-stripe';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { CategoriesComponent } from './category.component';
import { CategoryService, TutorService, WebinarService } from 'src/app/services';
import { CategoriesRoutingModule } from './category.routing';
import { CourseModule } from '../course/course.module';
let CategoryModule = class CategoryModule {
};
CategoryModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            ReactiveFormsModule,
            NgbModule,
            TranslateModule.forChild(),
            NgxStripeModule.forRoot(),
            CategoriesRoutingModule,
            WebinarModule,
            SlickCarouselModule,
            CourseModule
        ],
        declarations: [CategoriesComponent],
        providers: [WebinarService, CategoryService, TutorService],
        exports: []
    })
], CategoryModule);
export { CategoryModule };
