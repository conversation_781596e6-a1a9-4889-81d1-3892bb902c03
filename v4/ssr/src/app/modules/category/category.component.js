var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import * as _ from 'lodash';
let CategoriesComponent = class CategoriesComponent {
    constructor(webinarsService, route, appService, tutorService, seoService, courseService) {
        this.webinarsService = webinarsService;
        this.route = route;
        this.appService = appService;
        this.tutorService = tutorService;
        this.seoService = seoService;
        this.courseService = courseService;
        this.webinars = [];
        this.courses = [];
        this.webinarPagination = {
            total: 0,
            page: 0,
            pageSize: 6
        };
        this.coursePagination = {
            total: 0,
            page: 0,
            pageSize: 6
        };
        this.showWebinarList = true;
        this.showCourseList = true;
        this.categories = [];
        this.searchedCategories = [];
        this.selectedCategoryIds = [];
        this.tutors = [];
        this.seoService.setMetaTitle('Categories');
        this.route.queryParams.subscribe((params) => {
            if (params.categoryIds && params.categoryIds.length) {
                params.categoryIds.split(',').forEach((id) => {
                    this.selectedCategoryIds.push(id);
                });
            }
            this.queryWebinars();
            this.queryCourses();
        });
    }
    ngOnInit() {
        const cate_data = this.route.snapshot.data['categories'];
        if (cate_data) {
            this.categories = this.searchedCategories = cate_data;
        }
        this.queryTutors();
    }
    queryCourses() {
        this.courseService
            .search({
            page: this.coursePagination.page,
            take: this.coursePagination.pageSize,
            sort: 'createdAt',
            sortType: 'desc',
            disabled: false,
            categoryIds: this.selectedCategoryIds.join(',')
        })
            .then((resp) => {
            this.coursePagination.total = resp.data.count;
            this.courses = resp.data.items;
        })
            .catch((e) => {
            this.appService.toastError(e);
        });
    }
    queryWebinars() {
        this.webinarsService
            .search({
            page: this.webinarPagination.page,
            take: this.webinarPagination.pageSize,
            sort: 'createdAt',
            sortType: 'desc',
            isOpen: true,
            disabled: false,
            categoryIds: this.selectedCategoryIds.join(',')
        })
            .then((resp) => {
            this.webinarPagination.total = resp.data.count;
            this.webinars = resp.data.items;
        })
            .catch((e) => {
            this.appService.toastError(e);
        });
    }
    queryTutors() {
        this.tutorService
            .search({
            page: 0,
            take: 10,
            sort: 'createdAt',
            sortType: 'asc',
            isHomePage: true
        })
            .then((resp) => {
            this.tutors = resp.data.items;
        })
            .catch(() => this.appService.toastError());
    }
    onChangeCategorySelect(id) {
        if (this.selectedCategoryIds.includes(id)) {
            this.selectedCategoryIds = this.selectedCategoryIds.filter((item) => item !== id);
        }
        else
            this.selectedCategoryIds.push(id);
        this.webinarPagination.page = 0;
        this.coursePagination.page = 0;
        this.queryWebinars();
        this.queryCourses();
    }
    handleOnSearchCategory(event) {
        const debounceSearch = _.debounce((event) => {
            this.searchedCategories = this.categories.filter((item) => item.name.toLowerCase().includes(event.target.value.toLowerCase()));
        }, 500);
        debounceSearch(event);
    }
    pageChange(type) {
        if (type === 'webinar') {
            $('html, body').animate({
                scrollTop: $('#webinar-list').offset().top
            }, 800);
            this.queryWebinars();
        }
        else {
            this.queryCourses();
            $('html, body').animate({
                scrollTop: $('#course-list').offset().top
            }, 800);
        }
    }
};
CategoriesComponent = __decorate([
    Component({
        templateUrl: './category.component.html'
    })
], CategoriesComponent);
export { CategoriesComponent };
