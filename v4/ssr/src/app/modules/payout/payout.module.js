var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PayoutRoutingModule } from './payout.routing';
import { ListingRequestComponent, ListingAccountsComponent, CreateRequestPayoutComponent, AccountUpdateComponent, AccountCreateComponent, PayoutMenuComponent } from './components';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { RequestPayoutModalComponent } from './components/modal-request/modal-request.component';
import { SharedModule } from 'src/app/shared.module';
import { SortComponent } from 'src/app/components/uis/sort/sort.component';
import { StatusComponent } from 'src/app/components/uis/status.component';
let PayoutModule = class PayoutModule {
};
PayoutModule = __decorate([
    NgModule({
        imports: [
            CommonModule,
            FormsModule,
            PayoutRoutingModule,
            NgbModule,
            SharedModule,
            TranslateModule.forChild(),
            NgxChartsModule,
            SortComponent,
            StatusComponent
        ],
        declarations: [
            ListingAccountsComponent,
            ListingRequestComponent,
            AccountUpdateComponent,
            CreateRequestPayoutComponent,
            AccountCreateComponent,
            PayoutMenuComponent,
            RequestPayoutModalComponent
        ],
        exports: []
    })
], PayoutModule);
export { PayoutModule };
