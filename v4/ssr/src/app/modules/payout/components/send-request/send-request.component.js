var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Output, EventEmitter } from '@angular/core';
import { STATE } from 'src/app/services';
let CreateRequestPayoutComponent = class CreateRequestPayoutComponent {
    constructor(router, payoutService, appService, seoService, authService, stateService) {
        this.router = router;
        this.payoutService = payoutService;
        this.appService = appService;
        this.seoService = seoService;
        this.authService = authService;
        this.stateService = stateService;
        this.payoutAccountId = '';
        this.accounts = [];
        this.doRequest = new EventEmitter();
        this.seoService.setMetaTitle('Send request');
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.authService.getCurrentUser().then((resp) => {
            this.tutorId = resp.id;
            this.getBalance({ tutorId: this.tutorId });
        });
        this.payoutService
            .findAccount({
            take: 50,
            sortBy: 'createdAt',
            sortType: 'desc'
        })
            .then((res) => {
            this.accounts = res.data.items;
            this.payoutAccountId = this.accounts[0]._id;
        })
            .catch(() => this.appService.toastError());
    }
    getBalance(params) {
        this.payoutService
            .getBalance(params)
            .then((resp) => {
            this.balance = resp.data;
        })
            .catch(() => this.appService.toastError());
    }
    submit() {
        if (!this.payoutAccountId) {
            return this.appService.toastError('Please enter Payout Account Id');
        }
        this.payoutService
            .create({ payoutAccountId: this.payoutAccountId })
            .then((res) => {
            this.doRequest.emit(res.data);
            this.appService.toastSuccess('Your request has been sent.');
            this.router.navigate(['/users/payout/request']);
        })
            .catch((err) => {
            this.appService.toastError(err);
        });
    }
};
__decorate([
    Output()
], CreateRequestPayoutComponent.prototype, "doRequest", void 0);
CreateRequestPayoutComponent = __decorate([
    Component({
        selector: 'app-create-request-payout',
        templateUrl: './form.html'
    })
], CreateRequestPayoutComponent);
export { CreateRequestPayoutComponent };
