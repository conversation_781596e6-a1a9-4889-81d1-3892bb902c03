var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
import { STATE } from 'src/app/services';
let RequestPayoutModalComponent = class RequestPayoutModalComponent {
    constructor(stateService, activeModal, router, toast, translate) {
        this.stateService = stateService;
        this.activeModal = activeModal;
        this.router = router;
        this.toast = toast;
        this.translate = translate;
        this.accounts = [];
        this.config = {};
        this.payoutAccountId = '';
    }
    ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    submitRequest() {
        if (!this.payoutAccountId) {
            return this.toast.error(this.translate.instant('Please enter Payout Account Id'));
        }
        this.activeModal.close({
            payoutAccountId: this.payoutAccountId
        });
    }
};
__decorate([
    Input()
], RequestPayoutModalComponent.prototype, "balance", void 0);
RequestPayoutModalComponent = __decorate([
    Component({
        selector: 'app-modal-request-payout',
        templateUrl: './modal-request.html'
    })
], RequestPayoutModalComponent);
export { RequestPayoutModalComponent };
