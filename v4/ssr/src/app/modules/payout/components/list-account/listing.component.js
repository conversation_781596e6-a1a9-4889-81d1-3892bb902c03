var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let ListingAccountsComponent = class ListingAccountsComponent {
    constructor(accountService, appService, seoService, translate) {
        this.accountService = accountService;
        this.appService = appService;
        this.seoService = seoService;
        this.translate = translate;
        this.accounts = [];
        this.page = 1;
        this.pageSize = 10;
        this.total = 0;
        this.searchFields = {};
        this.searchType = '';
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = false;
        this.seoService.setMetaTitle('Accounts manager');
    }
    ngOnInit() {
        this.query();
    }
    query() {
        this.loading = true;
        this.accountService
            .find({
            page: this.page,
            take: this.pageSize,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            type: this.searchType
        })
            .then((res) => {
            this.accounts = res.data.items;
            this.total = res.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    keyPress(event) {
        if (event.charCode === 13) {
            this.query();
        }
    }
    remove(itemId, index) {
        if (window.confirm(this.translate.instant('Are you sure to delete this item?'))) {
            this.accountService
                .remove(itemId)
                .then(() => {
                this.appService.toastSuccess('Item has been deleted!');
                this.accounts.splice(index, 1);
            })
                .catch((err) => this.appService.toastError(err));
        }
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            this.query();
        }, 400);
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
};
ListingAccountsComponent = __decorate([
    Component({
        selector: 'app-account-listing',
        templateUrl: './listing.html'
    })
], ListingAccountsComponent);
export { ListingAccountsComponent };
