var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { RequestPayoutModalComponent } from '../modal-request/modal-request.component';
import { STATE } from 'src/app/services';
import { ScaleType } from '@swimlane/ngx-charts';
let ListingRequestComponent = class ListingRequestComponent {
    constructor(payoutService, appService, seoService, accountService, route, stateService, modalService) {
        this.payoutService = payoutService;
        this.appService = appService;
        this.seoService = seoService;
        this.accountService = accountService;
        this.route = route;
        this.stateService = stateService;
        this.modalService = modalService;
        this.items = [];
        this.page = 1;
        this.take = 10;
        this.total = 0;
        this.dateChange = {};
        this.searchFields = {};
        this.payoutAccountId = '';
        this.accounts = [];
        this.sortOption = {
            sortBy: 'createdAt',
            sortType: 'desc'
        };
        this.loading = false;
        this.config = {};
        this.view = [700, 400];
        this.gradient = false;
        this.showLegend = true;
        this.showLabels = true;
        this.isDoughnut = false;
        this.colorScheme = {
            name: 'myScheme',
            selectable: true,
            group: ScaleType.Ordinal,
            domain: ['#5AA454', '#A10A28', '#C7B42C']
        };
        this.loadingBalance = true;
        this.tab = 'earning';
        this.seoService.setMetaTitle('Payout Request Manager');
        this.accounts = this.route.snapshot.data['account'];
    }
    ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        if (this.currentUser && this.currentUser._id) {
            this.tutorId = this.currentUser._id;
            this.queryStats();
            this.queryBalance({ tutorId: this.tutorId });
            this.query();
        }
    }
    query() {
        this.loading = true;
        this.payoutService
            .search({
            page: this.page,
            take: this.take,
            sort: `${this.sortOption.sortBy}`,
            sortType: `${this.sortOption.sortType}`,
            tutorId: this.tutorId,
            ...this.searchFields
        })
            .then((resp) => {
            this.items = resp.data.items;
            this.total = resp.data.count;
            this.loading = false;
        })
            .catch(() => {
            this.loading = false;
            this.appService.toastError();
        });
    }
    queryStats() {
        this.payoutService
            .stats({ tutorId: this.tutorId, ...this.dateChange })
            .then((resp) => {
            this.stats = resp.data;
        });
    }
    queryBalance(params) {
        this.loadingBalance = true;
        this.payoutService
            .getBalance(params)
            .then((resp) => {
            this.balance = resp.data;
            this.balance.balance = parseFloat(resp.data.balance.toFixed(2));
            this.balance.total = parseFloat(resp.data.total.toFixed(2));
            this.balance.commission = parseFloat(resp.data.commission.toFixed(2));
            this.single = Object.keys(this.balance).map((item) => {
                const name = `${item.toUpperCase()}`;
                return {
                    name: name,
                    value: this.balance[item],
                    extra: {
                        currency: this.config.currencySymbol
                            ? this.config.currencySymbol
                            : '$'
                    }
                };
            });
            this.loadingBalance = false;
        })
            .catch((e) => {
            this.appService.toastError(e);
        });
    }
    formatTooltipText(data) {
        return `<span>${data.data.name}</br>${data.data.extra.currency + ' ' + data.value}</span>`;
    }
    sortBy(field, type) {
        this.sortOption.sortBy = field;
        this.sortOption.sortType = type;
        this.query();
    }
    dateChangeEvent(dateChange) {
        if (!dateChange) {
            if (this.dateChange.startDate && this.dateChange.toDate) {
                delete this.dateChange.startDate;
                delete this.dateChange.toDate;
            }
        }
        else {
            this.dateChange = {
                startDate: dateChange.from,
                toDate: dateChange.to
            };
        }
    }
    submitRequest() {
        this.modalRef = this.modalService.open(RequestPayoutModalComponent, {
            size: 'lg',
            centered: true
        });
        this.modalRef.componentInstance.balance = this.balance;
        this.modalRef.componentInstance.accounts = this.accounts;
        this.modalRef.result.then((res) => {
            if (!res.payoutAccountId) {
                return this.appService.toastError('Please enter Payout Account Id');
            }
            this.payoutAccountId = res.payoutAccountId;
            this.payoutService
                .create({ payoutAccountId: this.payoutAccountId })
                .then((pRes) => {
                this.items.push(pRes.data);
                this.appService.toastSuccess('Your request has been sent.');
                // this.router.navigate(['/users/payout/request']);
            })
                .catch((err) => {
                this.appService.toastError(err);
            });
        }, () => {
            return;
        });
    }
    onSort(evt) {
        this.sortOption = evt;
        this.query();
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.query();
    }
    onTabSelect(tab) {
        this.tab = tab;
    }
    ngAfterViewInit() {
        this.view = [$('.chart-container').innerWidth() / 1.35, 400];
    }
    ngOnDestroy() {
        this.modalRef && this.modalRef.dismiss();
    }
    onResize(event) {
        this.view = [event.target.innerWidth / 1.35, 400];
    }
};
ListingRequestComponent = __decorate([
    Component({
        selector: 'app-request-payout-listing',
        templateUrl: './listing.html'
    })
], ListingRequestComponent);
export { ListingRequestComponent };
