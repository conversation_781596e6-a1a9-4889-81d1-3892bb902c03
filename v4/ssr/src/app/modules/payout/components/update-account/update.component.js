var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let AccountUpdateComponent = class AccountUpdateComponent {
    constructor(accountService, route, appService, seoService) {
        this.accountService = accountService;
        this.route = route;
        this.appService = appService;
        this.seoService = seoService;
        this.isSubmitted = false;
        this.seoService.setMetaTitle('Update payout account');
    }
    ngOnInit() {
        const id = this.route.snapshot.paramMap.get('id');
        this.accountService.findOne(id).then((resp) => {
            this.account = resp.data;
        });
    }
    submit(frm) {
        this.isSubmitted = true;
        if (frm.$invalid) {
            this.appService.toastError('Invalid form, please try again.');
        }
        if (this.account.type === 'paypal' &&
            this.account.paypalAccount?.trim() === '') {
            return this.appService.toastError('If you select type payout is paypal, please enter Paypal Account');
        }
        else if (this.account.type === 'bank-account' &&
            this.account.paypalAccount) {
            this.account.paypalAccount = '';
        }
        this.account._id &&
            this.accountService
                .update(this.account._id, {
                type: this.account.type,
                paypalAccount: this.account.paypalAccount,
                accountHolderName: this.account.accountHolderName,
                accountNumber: this.account.accountNumber,
                iban: this.account.iban,
                bankName: this.account.bankName,
                bankAddress: this.account.bankAddress,
                sortCode: this.account.sortCode,
                routingNumber: this.account.routingNumber,
                swiftCode: this.account.swiftCode,
                ifscCode: this.account.ifscCode,
                routingCode: this.account.routingCode
            })
                .then(() => {
                this.appService.toastSuccess('Updated successfully.');
            });
    }
};
AccountUpdateComponent = __decorate([
    Component({
        selector: 'app-account-update',
        templateUrl: '../create-account/form.html'
    })
], AccountUpdateComponent);
export { AccountUpdateComponent };
