var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
let AccountCreateComponent = class AccountCreateComponent {
    constructor(router, accountService, appService, seoService, route) {
        this.router = router;
        this.accountService = accountService;
        this.appService = appService;
        this.seoService = seoService;
        this.route = route;
        this.isSubmitted = false;
        this.accounts = [];
        this.account = {
            type: 'bank-account',
            paypalAccount: '',
            accountHolderName: '',
            accountNumber: '',
            iban: '',
            bankName: '',
            bankAddress: '',
            sortCode: '',
            routingNumber: '',
            swiftCode: '',
            ifscCode: '',
            routingCode: ''
        };
        this.seoService.setMetaTitle('Create account');
    }
    ngOnInit() {
        this.accounts = this.route.snapshot.data['account'];
    }
    submit(frm) {
        this.isSubmitted = true;
        if (frm.invalid) {
            return this.appService.toastError('Form is invalid, please try again.');
        }
        if (this.account.type === 'paypal' &&
            this.account.paypalAccount?.trim() === '') {
            return this.appService.toastError('If you select type payout is paypal, please enter Paypal Account');
        }
        this.accountService.create(this.account).then(() => {
            this.appService.toastSuccess('Account has been created');
            this.router.navigate(['/users/payout/account']);
        }, (err) => this.appService.toastError(err));
    }
};
AccountCreateComponent = __decorate([
    Component({
        selector: 'app-account-create',
        templateUrl: './form.html'
    })
], AccountCreateComponent);
export { AccountCreateComponent };
