var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import * as _ from 'lodash';
import { ageFilter } from 'src/app/lib';
import { STATE } from 'src/app/services';
import * as jQuery from 'jquery';
let CourseListComponent = class CourseListComponent {
    constructor(courseService, route, seoService, router, gradeService, subjectService, topicService, stateService, appService) {
        this.courseService = courseService;
        this.route = route;
        this.seoService = seoService;
        this.router = router;
        this.gradeService = gradeService;
        this.subjectService = subjectService;
        this.topicService = topicService;
        this.stateService = stateService;
        this.appService = appService;
        this.page = 1;
        this.totalCourses = 0;
        this.items = [];
        this.currentPage = 1;
        this.pageSize = 12;
        this.showMoreFilter = false;
        this.searchFields = {
            categoryIds: '',
            gradeIds: '',
            subjectIds: '',
            topicIds: ''
        };
        this.sortOption = { sortBy: '', sortType: '' };
        this.loading = false;
        this.categories = [];
        this.itemChunks = [];
        this.dateChange = {};
        this.grades = [];
        this.total = 0;
        this.subjects = [];
        this.topics = [];
        this.ageFilter = ageFilter;
        this.seoService.setMetaTitle('List Courses');
        this.route.queryParams.subscribe((params) => {
            this.currentPage = params.page ? parseInt(params.page, 10) : 1;
            if (params?.categoryIds) {
                this.searchFields = {
                    ...this.searchFields,
                    categoryIds: params.categoryIds
                };
            }
            this.query();
        });
    }
    ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
        this.categories = this.route.snapshot.data['categories'];
        this.gradeService
            .search({
            take: 100,
            sort: 'ordering',
            sortType: 'asc'
        })
            .then((resp) => {
            this.grades = resp.data.items;
        });
    }
    showMore() {
        this.showMoreFilter = !this.showMoreFilter;
    }
    query() {
        this.loading = true;
        this.courseService
            .search({
            page: this.currentPage,
            take: this.pageSize,
            isOpen: true,
            approved: true,
            disabled: false,
            ...(this.sortOption.sortType && this.sortOption.sortBy
                ? {
                    sort: `${this.sortOption.sortBy}`,
                    sortType: `${this.sortOption.sortType}`
                }
                : { sort: 'createdAt', sortType: 'desc' }),
            ...this.searchFields,
            ...this.dateChange
        })
            .then((resp) => {
            this.totalCourses = resp.data.count;
            this.items = resp.data.items;
            this.itemChunks = _.chunk(this.items, 6);
            this.item1 =
                this.itemChunks.length && this.itemChunks[0].length
                    ? this.itemChunks[0][0]
                    : null;
            this.loading = false;
            jQuery(document).ready(function () {
                const showChar = 150; // How many characters are shown by default
                const ellipsestext = '...';
                // const moretext = 'Read More...';
                let content = '';
                $('.more1').each(function () {
                    content = $(this).text();
                    if (content.length > showChar) {
                        const c = content.substr(0, showChar);
                        const html = c +
                            '<span class="moreellipses">' +
                            ellipsestext +
                            '&nbsp;</span>' +
                            '</span>&nbsp;&nbsp;' +
                            '</span>';
                        $(this).html(html);
                    }
                });
            });
        })
            .catch((e) => {
            this.loading = false;
            this.appService.toastError(e);
        });
    }
    sortBy(field, type) {
        this.sortOption = {
            sortBy: field,
            sortType: type
        };
        this.query();
    }
    sortPrice(evt) {
        const value = evt.target.value;
        if (value) {
            this.sortOption = {
                sortBy: 'price',
                sortType: value
            };
        }
        this.query();
    }
    doSearch(evt) {
        const searchText = evt.target.value; // this is the search text
        if (this.timeout) {
            window.clearTimeout(this.timeout);
        }
        this.timeout = window.setTimeout(() => {
            this.searchFields.name = searchText;
            if (this.currentPage > 1) {
                this.router.navigate([], {
                    queryParams: { page: 1 },
                    queryParamsHandling: 'merge'
                });
            }
            else {
                this.query();
            }
        }, 400);
    }
    dateChangeEvent(dateChange) {
        if (!dateChange) {
            if (this.dateChange.startTime && this.dateChange.toTime) {
                delete this.dateChange.startTime;
                delete this.dateChange.toTime;
                if (this.currentPage > 1) {
                    this.router.navigate([], {
                        queryParams: { page: 1 },
                        queryParamsHandling: 'merge'
                    });
                }
                else {
                    this.query();
                }
            }
        }
        else {
            this.dateChange = {
                startTime: dateChange.from,
                toTime: dateChange.to
            };
            if (this.currentPage > 1) {
                this.router.navigate([], {
                    queryParams: { page: 1 },
                    queryParamsHandling: 'merge'
                });
            }
            else {
                this.query();
            }
        }
    }
    getCategories(course) {
        let categories = '';
        if (course.categories.length > 0) {
            course.categories.forEach((cat) => {
                categories = categories + cat.name + ', ';
            });
            categories = categories.slice(0, -2);
        }
        return categories;
    }
    gradeChange() {
        if (this.currentPage > 1) {
            this.router.navigate([], {
                queryParams: { page: 1 },
                queryParamsHandling: 'merge'
            });
        }
        else {
            this.query();
        }
    }
    selectCategory() {
        if (this.searchFields.categoryIds) {
            this.querySubjects();
            this.searchFields.topicIds = [];
        }
        else {
            this.searchFields.subjectIds = [];
            this.searchFields.topicIds = [];
            this.subjects = [];
            this.topics = [];
        }
        if (this.currentPage > 1) {
            this.router.navigate([], {
                queryParams: { page: 1 },
                queryParamsHandling: 'merge'
            });
        }
        else {
            this.query();
        }
    }
    querySubjects() {
        this.subjectService
            .search({
            categoryIds: this.searchFields.categoryIds,
            take: 1000,
            isActive: true
        })
            .then((resp) => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.subjects = resp.data.items;
            }
            else {
                this.subjects = [];
            }
        });
    }
    selectSubject() {
        if (this.searchFields.subjectIds) {
            this.queryTopic();
        }
        else {
            this.searchFields.topicIds = [];
            this.topics = [];
        }
        if (this.currentPage > 1) {
            this.router.navigate([], {
                queryParams: { page: 1 },
                queryParamsHandling: 'merge'
            });
        }
        else {
            this.query();
        }
    }
    queryTopic() {
        this.topicService
            .search({
            subjectIds: this.searchFields.subjectIds,
            take: 1000,
            isActive: true
        })
            .then((resp) => {
            if (resp.data && resp.data.items && resp.data.items.length > 0) {
                this.topics = resp.data.items;
            }
            else {
                this.topics = [];
            }
        });
    }
    filterByAge(event) {
        if (event) {
            this.searchFields = { ...this.searchFields, age: JSON.stringify(event) };
        }
        else
            this.searchFields.age = '';
        if (this.currentPage > 1) {
            this.router.navigate([], {
                queryParams: { page: 1 },
                queryParamsHandling: 'merge'
            });
        }
        else {
            this.query();
        }
    }
    pageChange() {
        $('html, body').animate({ scrollTop: 0 });
        this.router.navigate(['/courses'], {
            queryParams: { page: this.currentPage },
            queryParamsHandling: 'merge'
        });
    }
};
CourseListComponent = __decorate([
    Component({
        selector: 'app-course-listing',
        templateUrl: './list.html'
    })
], CourseListComponent);
export { CourseListComponent };
