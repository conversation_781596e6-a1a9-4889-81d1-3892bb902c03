var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CourseListComponent } from './list/list.component';
import categoriesResolver from 'src/app/services/resolvers/category.resolver';
import { CourseDetailComponent } from './details/detail.component';
import courseDetailResolver from 'src/app/services/resolvers/course-detail.resolver';
const routes = [
    {
        path: '',
        component: CourseListComponent,
        resolve: {
            categories: categoriesResolver
        }
    },
    {
        path: ':id',
        component: CourseDetailComponent,
        resolve: {
            course: courseDetailResolver
        }
    }
];
let CourseRoutingModule = class CourseRoutingModule {
};
CourseRoutingModule = __decorate([
    NgModule({
        imports: [RouterModule.forChild(routes)],
        exports: [RouterModule]
    })
], CourseRoutingModule);
export { CourseRoutingModule };
