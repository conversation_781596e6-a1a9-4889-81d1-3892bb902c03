var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import categoriesResolver from 'src/app/services/resolvers/category.resolver';
import { WebinarListingComponent } from './list/list.component';
import { DetailWebinarComponent } from './details/detail.component';
import webinarDetailResolver from 'src/app/services/resolvers/webinar-detail.resolver';
const routes = [
    {
        path: '',
        component: WebinarListingComponent,
        resolve: {
            categories: categoriesResolver
        }
    },
    {
        path: ':id',
        component: DetailWebinarComponent,
        resolve: {
            webinar: webinarDetailResolver
        }
    }
];
let WebinarRoutingModule = class WebinarRoutingModule {
};
WebinarRoutingModule = __decorate([
    NgModule({
        imports: [RouterModule.forChild(routes)],
        exports: [RouterModule]
    })
], WebinarRoutingModule);
export { WebinarRoutingModule };
