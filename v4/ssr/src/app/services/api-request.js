var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var APIRequest_1;
import { Injectable, inject } from '@angular/core';
import { isUrl } from 'src/lib/string';
import { lastValueFrom } from 'rxjs';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { environment } from 'src/environments/environment';
let APIRequest = APIRequest_1 = class APIRequest {
    constructor(httpClient) {
        this.httpClient = httpClient;
        this.cookieService = inject(SsrCookieService);
    }
    getBaseApiEndpoint() {
        const { API_ENDPOINT } = APIRequest_1;
        if (API_ENDPOINT)
            return API_ENDPOINT;
        const { apiBaseUrl } = environment;
        return apiBaseUrl;
    }
    /**
     * Checks if a network request came back fine, and throws an error if not
     *
     * @param  {object} response   A response from a network request
     *
     * @return {object|undefined} Returns either the response, or throws an error
     */
    async checkStatus(response) {
        if (response.status >= 200 && response.status < 300) {
            return response;
        }
        if (response.status === 401) {
            await this.cookieService.delete('accessToken');
            await this.cookieService.delete('isLoggedin');
            window.location.href = '/auth/login';
            throw new Error('Please login!');
        }
        if (response.status === 404) {
            window.location.href = 'pages/404-not-found';
            throw new Error('Not found!');
        }
        throw response.error;
    }
    buildUrl(baseUrl, params) {
        if (!params) {
            return baseUrl;
        }
        const queryString = Object.keys(params)
            .map((k) => {
            if (Array.isArray(params[k])) {
                return params[k]
                    .map((param) => `${encodeURIComponent(k)}=${param}`)
                    .join('&');
            }
            return `${encodeURIComponent(k)}=${params[k]}`;
        })
            .join('&');
        return `${baseUrl}?${encodeURI(queryString)}`;
    }
    async request(url, method, body, headers) {
        const verb = (method || 'get').toUpperCase();
        const updatedHeader = {
            'Content-Type': 'application/json',
            ...(headers || {})
        };
        const baseApiEndpoint = this.getBaseApiEndpoint();
        const updatedUrl = isUrl(url) ? url : `${baseApiEndpoint}${url}`;
        return lastValueFrom(this.httpClient.request(verb, updatedUrl, {
            headers: updatedHeader,
            body: body ? JSON.stringify(body) : null
        }))
            .then((resp) => resp)
            .catch((err) => {
            throw err?.error;
        });
    }
    get(url, headers) {
        return this.request(url, 'get', null, headers);
    }
    post(url, data, headers) {
        return this.request(url, 'post', data, headers);
    }
    put(url, data, headers) {
        return this.request(url, 'put', data, headers);
    }
    del(url, data, headers) {
        return this.request(url, 'delete', data, headers);
    }
};
APIRequest.token = '';
APIRequest.API_ENDPOINT = '';
APIRequest = APIRequest_1 = __decorate([
    Injectable({
        providedIn: 'root'
    })
], APIRequest);
export { APIRequest };
