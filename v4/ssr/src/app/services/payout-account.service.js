var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let AccountService = class AccountService extends APIRequest {
    find(params) {
        return this.get(this.buildUrl('/payout/accounts', params));
    }
    create(data) {
        return this.post('/payout/accounts', data);
    }
    remove(id) {
        return this.del(`/payout/accounts/${id}`);
    }
    findOne(id) {
        return this.get(`/payout/accounts/${id}`);
    }
    update(id, data) {
        return this.put(`/payout/accounts/${id}`, data);
    }
};
AccountService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], AccountService);
export { AccountService };
