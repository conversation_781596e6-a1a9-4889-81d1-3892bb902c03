var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
/**
 * Simple state management service to replace @angular-extensions/model
 * Provides the same API but with modern Angular patterns
 */
export class Model {
    constructor(initialData) {
        this.initialData = initialData;
        this._data$ = new BehaviorSubject(this.initialData);
    }
    get data$() {
        return this._data$.asObservable();
    }
    get() {
        return this._data$.value;
    }
    set(data) {
        // Create immutable copy to prevent accidental mutations
        const immutableData = JSON.parse(JSON.stringify(data));
        this._data$.next(immutableData);
    }
}
let ModelFactory = class ModelFactory {
    create(initialData) {
        return new Model(initialData);
    }
    createMutable(initialData) {
        // For compatibility - same as create but could be optimized later
        return new Model(initialData);
    }
};
ModelFactory = __decorate([
    Injectable({
        providedIn: 'root'
    })
], ModelFactory);
export { ModelFactory };
