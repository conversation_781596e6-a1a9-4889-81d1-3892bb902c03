var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let ReviewService = class ReviewService extends APIRequest {
    create(data) {
        return this.post('/reviews', data);
    }
    list(params) {
        return this.get(this.buildUrl('/reviews', params));
    }
    findOne(id) {
        return this.get(`/reviews/${id}`);
    }
    current(itemId, params) {
        return this.get(this.buildUrl(`/reviews/${itemId}/current`, params));
    }
    update(id, data) {
        return this.put(`/reviews/${id}`, data);
    }
    delete(id) {
        return this.del(`/reviews/${id}`);
    }
    findByRateToAndRateBy(query) {
        return this.get(this.buildUrl('/reviews/findOne', query));
    }
};
ReviewService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], ReviewService);
export { ReviewService };
