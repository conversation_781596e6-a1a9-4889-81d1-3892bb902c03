var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { APIRequest } from './api-request';
let NotificationService = class NotificationService extends APIRequest {
    constructor() {
        super(...arguments);
        this.readNotification = new Subject();
        this.readNotification$ = this.readNotification.asObservable();
    }
    list(params) {
        return this.get(this.buildUrl('/notifications', params));
    }
    read(notificationId) {
        return this.post(`/notification/read/${notificationId}`);
    }
    remove(notificationId) {
        return this.del(`/notification/remove/${notificationId}`);
    }
    readAll() {
        return this.post('/notification/read-all');
    }
    countUnread() {
        return this.get('/notifications/count-unread');
    }
    onReadNotificationSuccess(value) {
        this.readNotification.next(value);
    }
};
NotificationService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], NotificationService);
export { NotificationService };
