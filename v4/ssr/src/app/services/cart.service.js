var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable, inject } from '@angular/core';
import * as moment from 'moment';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
let CartService = class CartService {
    constructor(modelFactory) {
        this.modelFactory = modelFactory;
        this.cookieService = inject(SsrCookieService);
        this.model = this.modelFactory.create({
            tutorId: '',
            items: []
        });
        this.cart$ = this.model.data$;
    }
    getCart() {
        const cart = this.model.get();
        return cart;
    }
    getTutorId() {
        const tutorId = this.model.get().tutorId;
        return tutorId;
    }
    getItemsInCart(type = '') {
        const items = this.model.get().items;
        if (type) {
            return items.filter((item) => item.type === type);
        }
        return items;
    }
    updateTutorId(tutorId) {
        const model = this.model.get();
        const newModel = { ...model, tutorId: tutorId };
        // localStorage.setItem('cartInfo', JSON.stringify(newModel));
        this.cookieService.set('cartInfo', JSON.stringify(newModel));
        this.model.set(newModel);
    }
    updateCart(item) {
        const model = this.model.get();
        const newModel = {
            ...model,
            items: [...model.items, item]
        };
        // localStorage.setItem('cartInfo', JSON.stringify(newModel));
        this.cookieService.set('cartInfo', JSON.stringify(newModel));
        this.model.set(newModel);
    }
    updateCartInfoFromLocal(cart) {
        const model = this.model.get();
        const newModel = {
            ...model,
            ...cart
        };
        this.model.set(newModel);
    }
    removeItem(item) {
        const model = this.model.get();
        const { items } = model;
        const newItems = items.filter((i) => !moment(i.product.startTime).isSame(item.product.startTime));
        const newModel = {
            ...model,
            items: [...newItems]
        };
        // localStorage.setItem('cartInfo', JSON.stringify(newModel));
        this.cookieService.set('cartInfo', JSON.stringify(newModel));
        this.model.set(newModel);
    }
    removeCart() {
        const model = this.model.get();
        const newModel = {
            ...model,
            items: []
        };
        this.model.set(newModel);
    }
};
CartService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], CartService);
export { CartService };
