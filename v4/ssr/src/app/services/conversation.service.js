var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { APIRequest } from './api-request';
let ConversationService = class ConversationService extends APIRequest {
    constructor() {
        super(...arguments);
        this.conversationLoaded = new Subject();
        this.conversationLoaded$ = this.conversationLoaded.asObservable();
    }
    list(params) {
        return this.get(this.buildUrl('/messages/conversations', params));
    }
    create(recipientId) {
        return this.post('/messages/conversations', { recipientId });
    }
    setActive(conversation) {
        this.conversationLoaded.next(conversation);
    }
    read(conversationId, params) {
        return this.post(`/messages/conversations/${conversationId}/read`, params);
    }
    findOne(id) {
        return this.get(`/messages/conversation/${id}`);
    }
};
ConversationService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], ConversationService);
export { ConversationService };
