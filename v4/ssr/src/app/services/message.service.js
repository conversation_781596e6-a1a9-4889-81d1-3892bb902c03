var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { APIRequest } from './api-request';
let MessageService = class MessageService extends APIRequest {
    constructor() {
        super(...arguments);
        this.sendMessage = new Subject();
        this.sendMessage$ = this.sendMessage.asObservable();
    }
    listByConversation(conversationId, params) {
        return this.get(this.buildUrl(`/messages/conversations/${conversationId}`, params));
    }
    send(data) {
        return this.post('/messages', data);
    }
    afterSendSuccess(conversationId, message) {
        this.sendMessage.next({
            conversationId,
            message
        });
    }
};
MessageService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], MessageService);
export { MessageService };
