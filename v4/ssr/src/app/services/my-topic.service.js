var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let MyTopicService = class MyTopicService extends APIRequest {
    create(params) {
        return this.post('/my-topic', params);
    }
    search(params) {
        return this.get(this.buildUrl('/my-topics', params));
    }
    findOne(id) {
        return this.get(`/my-topic/${id}`);
    }
    update(id, data) {
        return this.put(`/my-topic/${id}`, data);
    }
    delete(id) {
        return this.del(`/my-topic/${id}`);
    }
    getListOfMe(params) {
        return this.get(this.buildUrl('/my-topics/me', params));
    }
    changeStatus(id) {
        return this.put(`/my-topic/${id}/change-status`);
    }
};
MyTopicService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], MyTopicService);
export { MyTopicService };
