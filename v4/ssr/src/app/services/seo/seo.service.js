var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
let SeoService = class SeoService {
    constructor(meta, titleService) {
        this.meta = meta;
        this.titleService = titleService;
    }
    setMetaDescription(content) {
        this.meta.updateTag({
            name: 'description',
            content: content
        });
    }
    setMetaTitle(title) {
        this.titleService.setTitle(title);
    }
    addMetaTags(metas) {
        if (metas.length) {
            metas.map((m) => {
                this.meta.updateTag(m);
            });
        }
    }
};
SeoService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], SeoService);
export { SeoService };
