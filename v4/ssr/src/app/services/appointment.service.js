var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let AppointmentService = class AppointmentService extends APIRequest {
    create(credentials) {
        return this.post('/appointments/book', credentials);
    }
    search(params) {
        return this.get(this.buildUrl('/appointments', params));
    }
    cancel(id, data) {
        return this.post(`/appointments/${id}/cancel`, data);
    }
    checkFree(data) {
        return this.post('/appointments/check/free', data);
    }
    findOne(id) {
        return this.get(`/appointments/${id}`);
    }
    appointmentTutor(tutorId, params) {
        return this.get(this.buildUrl(`/appointments/tutors/${tutorId}`, params));
    }
    checkOverlap(data) {
        return this.post('/appointments/check/overlap', data);
    }
    checkout(data) {
        return this.post('/appointments/checkout', data);
    }
    updateDocument(id, data) {
        return this.put(`/appointments/${id}/update-document`, data);
    }
    tutorCancel(appointmentId, data) {
        return this.post(`/appointments/tutor/${appointmentId}/cancel`, data);
    }
    studentCancel(appointmentId, data) {
        return this.post(`/appointments/student/${appointmentId}/cancel`, data);
    }
    canReschedule(appointmentId) {
        return this.post(`/appointments/${appointmentId}/canReschedule`);
    }
    reSchedule(appointmentId, data) {
        return this.put(`/appointments/${appointmentId}/reSchedule`, data);
    }
    startMeeting(appointmentId) {
        return this.post(`/meeting/start/${appointmentId}`);
    }
    joinMeeting(appointmentId) {
        return this.post(`/meeting/join/${appointmentId}`);
    }
    removeDocument(id, documentId) {
        return this.del(`/appointments/${id}/remove-document/${documentId}`);
    }
    report(data) {
        return this.post('/reports', data);
    }
    searchAppointmentWebinar(params) {
        return this.get(this.buildUrl('/appointments/webinar/aggregate', params));
    }
};
AppointmentService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], AppointmentService);
export { AppointmentService };
