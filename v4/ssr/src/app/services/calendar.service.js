var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { APIRequest } from './api-request';
let CalendarService = class CalendarService extends APIRequest {
    constructor() {
        super(...arguments);
        this.scheduleLoaded = new Subject();
        this.scheduleLoaded$ = this.scheduleLoaded.asObservable();
    }
    async search(params) {
        return this.get(this.buildUrl('/schedule', params));
    }
    async create(data) {
        return this.post('/schedule', data);
    }
    async update(id, data) {
        return this.put(`/schedule/${id}`, data);
    }
    async delete(id) {
        return this.del(`/schedule/${id}`);
    }
    async deleteByHash(hash) {
        return this.del(`/schedule/remove-by-hash/${hash}`);
    }
    async checkByHash(hash) {
        return this.post(`/schedule/check-by-hash/${hash}`);
    }
    async checkByWebinar(webinarId) {
        return this.post(`/schedule/check-by-webinar/${webinarId}`);
    }
    async createRecurring(data) {
        return this.post('/recurring-schedule', data);
    }
    async loadListRecurring(query) {
        return this.get(this.buildUrl('/recurring-schedule', query));
    }
    async removeRecurring(id) {
        return this.del(`/recurring-schedule/${id}`);
    }
    async all(params) {
        return this.get(this.buildUrl('/schedule', params));
    }
};
CalendarService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], CalendarService);
export { CalendarService };
