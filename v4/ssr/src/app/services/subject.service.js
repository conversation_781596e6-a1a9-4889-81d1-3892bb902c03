var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let SubjectService = class SubjectService extends APIRequest {
    constructor() {
        super(...arguments);
        this.subjects = null;
    }
    async getSubjects(params) {
        if (this.subjects) {
            return Promise.resolve(this.subjects);
        }
        if (this._getSubjects && typeof this._getSubjects.then === 'function') {
            return this._getSubjects;
        }
        this._getSubjects = await this.get(this.buildUrl('/subjects', params)).then(resp => {
            this.subjects = resp;
            return this.subjects;
        });
        return this._getSubjects;
    }
    async search(params) {
        return this.get(this.buildUrl('/subjects', params));
    }
};
SubjectService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], SubjectService);
export { SubjectService };
