var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let MyCategoryService = class MyCategoryService extends APIRequest {
    create(params) {
        return this.post('/my-category', params);
    }
    search(params) {
        return this.get(this.buildUrl('/my-categories', params));
    }
    findOne(id) {
        return this.get(`/my-category/${id}`);
    }
    update(id, data) {
        return this.put(`/my-category/${id}`, data);
    }
    delete(id) {
        return this.del(`/my-category/${id}`);
    }
    getListOfMe(params) {
        return this.get(this.buildUrl('/my-categories/me', params));
    }
    changeStatus(id) {
        return this.put(`/my-category/${id}/change-status`);
    }
};
MyCategoryService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], MyCategoryService);
export { MyCategoryService };
