var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let RequestPayoutService = class RequestPayoutService extends APIRequest {
    getBalance(params) {
        return this.get(this.buildUrl('/payout/balance', params));
    }
    search(params) {
        return this.get(this.buildUrl('/payout/requests', params));
    }
    create(data) {
        return this.post('/payout/requests', data);
    }
    stats(params) {
        return this.get(this.buildUrl('/payout/stats', params));
    }
    findAccount(params) {
        return this.get(this.buildUrl('/payout/accounts', params));
    }
};
RequestPayoutService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], RequestPayoutService);
export { RequestPayoutService };
