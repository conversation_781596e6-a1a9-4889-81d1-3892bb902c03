var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let TopicService = class TopicService extends APIRequest {
    constructor() {
        super(...arguments);
        this.topics = null;
    }
    async getTopics(params) {
        if (this.topics) {
            return Promise.resolve(this.topics);
        }
        if (this._getTopics && typeof this._getTopics.then === 'function') {
            return this._getTopics;
        }
        this._getTopics = await this.get(this.buildUrl('/topics', params))
            .then(resp => {
            this.topics = resp;
            return this.topics;
        });
        return this._getTopics;
    }
    search(params) {
        return this.get(this.buildUrl('/topics', params));
    }
    findOne(id) {
        return this.get(`/topics/${id}`);
    }
};
TopicService = __decorate([
    Injectable({ providedIn: 'root' })
], TopicService);
export { TopicService };
