var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let WebinarService = class WebinarService extends APIRequest {
    create(params) {
        return this.post('/webinars', params);
    }
    search(params) {
        return this.get(this.buildUrl('/webinars', params));
    }
    findOne(id) {
        return this.get(`/webinars/${id}`);
    }
    update(id, data) {
        return this.put(`/webinars/${id}`, data);
    }
    delete(id) {
        return this.del(`/webinars/${id}`);
    }
    checkUsedCoupon(params) {
        return this.post('/coupons/check-used-coupon', params);
    }
    findSingleCoupon(id, params) {
        return this.get(this.buildUrl(`/coupons/${id}`, params));
    }
    changeStatus(id) {
        return this.put(`/webinar/${id}/change-status`);
    }
    getLatest(id) {
        return this.get(`/webinars/${id}/latest`);
    }
    removeDocument(id, documentId) {
        return this.del(`/webinars/${id}/remove-document/${documentId}`);
    }
    checkOverlapWebinar(data) {
        return this.post('/webinars/check/overlap', data);
    }
    enroll(params) {
        return this.post('/enroll', params);
    }
    getEnrolledList(id) {
        return this.get(`/webinars/${id}/enrolled`);
    }
};
WebinarService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], WebinarService);
export { WebinarService };
