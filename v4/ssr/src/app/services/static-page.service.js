var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let StaticPageService = class StaticPageService extends APIRequest {
    constructor() {
        super(...arguments);
        this.pages = null;
    }
    async getPages(params) {
        if (this.pages) {
            return this.pages;
        }
        if (this._getPages && typeof this._getPages.then === 'function') {
            return this._getPages;
        }
        this._getPages = await this.get(this.buildUrl('/posts', params)).then((resp) => {
            this.pages = resp;
            return this.pages;
        });
        return this._getPages;
    }
    create(params) {
        return this.post('/posts', params);
    }
    search(params) {
        return this.get(this.buildUrl('/posts', params));
    }
    findOne(id) {
        return this.get(`/posts/${id}`);
    }
    update(id, data) {
        return this.post(`/posts/${id}`, data);
    }
    delete(id) {
        return this.del(`/posts/${id}`);
    }
};
StaticPageService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], StaticPageService);
export { StaticPageService };
