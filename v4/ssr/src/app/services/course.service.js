var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let CourseService = class CourseService extends APIRequest {
    create(params) {
        return this.post('/courses', params);
    }
    search(params) {
        return this.get(this.buildUrl('/courses', params));
    }
    findOne(id) {
        return this.get(`/courses/${id}`);
    }
    update(id, data) {
        return this.put(`/courses/${id}`, data);
    }
    delete(id) {
        return this.del(`/courses/${id}`);
    }
    enroll(params) {
        return this.post('/enroll', params);
    }
    gift(params) {
        return this.post('/gift', params);
    }
    checkUsedCoupon(id) {
        return this.get(`/coupons/check-used-coupon/${id}`);
    }
    applyCoupon(params) {
        return this.get(this.buildUrl('/coupon/apply-coupon', params));
    }
    getTransactions(tutorId, params) {
        return this.get(this.buildUrl(`/courses/${tutorId}/transaction`, params));
    }
    getEnrolledList(id) {
        return this.get(`/courses/${id}/enrolled`);
    }
    saveAsDraff(params) {
        return this.post('/courses/save-as-draff', params);
    }
};
CourseService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], CourseService);
export { CourseService };
