var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let CategoryService = class CategoryService extends APIRequest {
    constructor() {
        super(...arguments);
        this.categories = null;
    }
    async getCategories(params) {
        if (this.categories) {
            return this.categories;
        }
        if (this._getCategories && typeof this._getCategories.then === 'function') {
            return this._getCategories;
        }
        this._getCategories = await this.get(this.buildUrl('/categories', params)).then(resp => {
            this.categories = resp;
            return this.categories;
        });
        return this._getCategories;
    }
    create(params) {
        return this.post('/categories', params);
    }
    search(params) {
        return this.get(this.buildUrl('/categories', params));
    }
    findOne(id) {
        return this.get(`/categories/${id}`);
    }
    update(id, data) {
        return this.post(`/categories/${id}`, data);
    }
    delete(id) {
        return this.del(`/categories/${id}`);
    }
};
CategoryService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], CategoryService);
export { CategoryService };
