var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { ReplaySubject, Subject } from 'rxjs';
import { STATE } from './state-service';
import { APIRequest } from './api-request';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
let AuthService = class AuthService extends APIRequest {
    constructor(stateService, myHttpService, mycookie, platformId, socket) {
        super(myHttpService);
        this.stateService = stateService;
        this.myHttpService = myHttpService;
        this.mycookie = mycookie;
        this.platformId = platformId;
        this.socket = socket;
        this.accessToken = '';
        this.userLoaded = new Subject();
        this.userLoaded$ = this.userLoaded.asObservable();
        this.currentUserSubject = new ReplaySubject(1);
        this.isServer = false;
        this.isBrowser = false;
        this.isBrowser = isPlatformBrowser(this.platformId);
        this.isServer = isPlatformServer(this.platformId);
    }
    isFetched(data) {
        return typeof data !== 'undefined';
    }
    async initAppGetCurrentUser() {
        const token = this.mycookie.get('accessToken');
        if (!token) {
            this.currentUserSubject.next(null);
            return null;
        }
        if (this.isServer) {
            return this.getCurrentUser();
        }
        else {
            if (this.stateService.hasState(STATE.CURRENT_USER)) {
                this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
                this.socket.reconnect();
                if (this.isFetched(this.currentUser)) {
                    this.currentUserSubject.next(this.currentUser);
                }
                return this.currentUser;
            }
            else {
                return this.getCurrentUser();
            }
        }
        // if (this.isBrowser) {
        //   const token = this.mycookie.get('accessToken');
        //   if (!token) return null;
        //   const baseApiEndpoint = this.getBaseApiEndpoint();
        //   const updatedUrl = `${baseApiEndpoint}/users/me`;
        //   return lastValueFrom(this.myHttpService.get(updatedUrl))
        //     .then((resp: any) => {
        //       this.currentUser = resp.data;
        //       this.mycookie.set('isLoggedin', 'yes');
        //       this.mycookie.set('timeZone', resp.data.timezone);
        //       this.userLoaded.next(resp.data);
        //       this.stateService.saveState('currentUser', this.currentUser);
        //       return this.currentUser;
        //     })
        //     .catch(() => {
        //       this.mycookie.delete('accessToken');
        //       this.mycookie.delete('isLoggedin');
        //       return null;
        //     });
        // }
    }
    async getCurrentUser() {
        if (this.isFetched(this.currentUser)) {
            this.currentUserSubject.next(this.currentUser);
            this.stateService.saveState('currentUser', this.currentUser);
            return new Promise((resolve) => resolve(this.currentUser));
        }
        if (this._getUser && typeof this._getUser.then === 'function') {
            return this._getUser;
        }
        this._getUser = await this.get('/users/me').then((resp) => {
            this.currentUser = resp.data;
            this.mycookie.set('isLoggedin', 'yes', { path: '/' });
            this.mycookie.set('timeZone', resp.data.timezone, { path: '/' });
            this.userLoaded.next(resp.data);
            this.stateService.saveState('currentUser', this.currentUser);
            this.socket.reconnect();
            this.currentUserSubject.next(this.currentUser);
            return this.currentUser;
        }).catch(() => {
            this.mycookie.delete('accessToken', '/');
            this.mycookie.delete('isLoggedin', '/');
            this.stateService.removeState(STATE.CURRENT_USER);
            this.currentUserSubject.next(null);
            return null;
        });
        return this._getUser;
    }
    updateCurrentUser(current) {
        this.currentUser = { ...this.currentUser, ...current };
    }
    login(credentials) {
        return this.post('/auth/login', credentials).then((resp) => {
            this.mycookie.set('isLoggedin', 'yes', { path: '/' });
            this.mycookie.set('accessToken', resp.data.token, { path: '/' });
            return this.getCurrentUser();
        });
    }
    register(info) {
        return this.post('/auth/register', info);
    }
    getAccessToken() {
        if (!this.accessToken) {
            this.accessToken = this.mycookie.get('accessToken') || '';
        }
        return this.accessToken;
    }
    forgot(email) {
        return this.post('/auth/forgot', { email });
    }
    removeToken() {
        this.mycookie.delete('accessToken', '/');
        this.mycookie.delete('isLoggedin', '/');
    }
    isLoggedin() {
        return this.mycookie.get('isLoggedin') === 'yes';
    }
    registerTutor(info) {
        return this.post('/tutors/register', info);
    }
};
AuthService = __decorate([
    Injectable({
        providedIn: 'root'
    }),
    __param(2, Inject(SsrCookieService)),
    __param(3, Inject(PLATFORM_ID))
], AuthService);
export { AuthService };
