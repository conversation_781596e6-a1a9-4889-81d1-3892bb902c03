var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { makeStateKey } from '@angular/platform-browser';
export const STATE = {
    CONFIG: 'config',
    CURRENT_USER: 'currentUser'
};
let StateService = class StateService {
    constructor(transferState) {
        this.transferState = transferState;
    }
    saveState(key, data) {
        this.transferState.set(makeStateKey(key), data);
    }
    getState(key, defaultValue = null) {
        const state = this.transferState.get(makeStateKey(key), defaultValue);
        // this.transferState.remove(makeStateKey(key));
        return state;
    }
    hasState(key) {
        return this.transferState.hasKey(makeStateKey(key));
    }
    removeState(key) {
        return this.transferState.remove(makeStateKey(key));
    }
    showBooking() {
        const current = this.getState(STATE.CURRENT_USER);
        if (!current || (current && current.type === 'student'))
            return true;
        const config = this.getState(STATE.CONFIG);
        return current && current.type === 'tutor' && config.allowTutorBooking ? true : false;
    }
};
StateService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], StateService);
export { StateService };
