var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { STATE } from './state-service';
import { firstValueFrom } from 'rxjs';
import { isPlatformServer } from '@angular/common';
let SystemService = class SystemService {
    constructor(stateService, http, platformId, cookies) {
        this.stateService = stateService;
        this.http = http;
        this.platformId = platformId;
        this.cookies = cookies;
        this.appConfig = null;
        this.isServer = false;
        this.isServer = isPlatformServer(this.platformId);
    }
    async configs() {
        if (this.isServer) {
            this.appConfig = await this.fetchConfig();
            const userLang = this.cookies.get('userLang') ||
                this.appConfig.i18n.defaultLanguage ||
                'en';
            this.appConfig.userLang = userLang;
            this.stateService.saveState(STATE.CONFIG, this.appConfig);
        }
        else {
            if (this.stateService.hasState(STATE.CONFIG)) {
                this.appConfig = this.stateService.getState(STATE.CONFIG);
            }
            else {
                this.appConfig = await this.fetchConfig();
                const userLang = this.cookies.get('userLang') ||
                    this.appConfig.i18n.defaultLanguage ||
                    'en';
                this.appConfig.userLang = userLang;
                localStorage.setItem('currencySymbol', this.appConfig.currencySymbol);
                this.stateService.saveState(STATE.CONFIG, this.appConfig);
            }
        }
        return this.appConfig;
    }
    async fetchConfig() {
        const { apiBaseUrl } = this.stateService.getState('environment');
        const updatedUrl = apiBaseUrl
            ? `${apiBaseUrl}/system/configs/public`
            : 'http://localhost:9000/v1/system/configs/public';
        return await firstValueFrom(this.http.get(updatedUrl))
            .then((resp) => resp.data)
            .catch((err) => console.log('configerr>>>>', err));
    }
    setUserLang(lang) {
        this.cookies.set('userLang', lang, { path: '/' });
    }
    showBooking() {
        const current = this.stateService.getState(STATE.CURRENT_USER);
        if (!current || (current && current.type === 'student'))
            return true;
        const config = this.stateService.getState(STATE.CONFIG);
        return current && current.type === 'tutor' && config && config.allowTutorBooking ? true : false;
    }
};
SystemService = __decorate([
    Injectable({
        providedIn: 'root'
    }),
    __param(2, Inject(PLATFORM_ID))
], SystemService);
export { SystemService };
