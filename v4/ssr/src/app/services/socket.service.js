var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID, inject } from '@angular/core';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { Socket } from 'ngx-socket-io';
import { environment } from 'src/environments/environment';
let SocketService = class SocketService {
    constructor(platformId) {
        this.platformId = platformId;
        this.config = {
            url: environment.socketUrl,
            options: {
                query: {},
                transports: ['websocket']
            }
        };
        this.cookieService = inject(SsrCookieService);
        this.socket = null;
    }
    getMessage(cb) {
        return this.socket && this.socket.on('new_message', (data) => {
            cb(data);
        });
    }
    connect() {
        return this.socket && this.socket.connect();
    }
    disconnect() {
        return this.socket && this.socket.disconnect();
    }
    emit(eventName, data, cb) {
        this.socket && this.socket.emit(eventName, data, (resp) => {
            cb && cb(resp);
        });
    }
    on(event, handle) {
        this.socket && this.socket.on(event, handle);
    }
    off(event, cb = () => { return; }) {
        this.socket && this.socket.removeListener(event, cb);
    }
    async reconnect() {
        if (isPlatformBrowser(this.platformId)) {
            if (this.socket !== null && this.socket instanceof Socket) {
                return this.socket;
            }
            else {
                const config = {
                    url: environment.socketUrl,
                    options: {
                        transports: ['websocket'],
                        query: {
                            token: this.cookieService.get('accessToken')
                        }
                    }
                };
                this.socket = new Socket(config);
                return this.socket;
            }
        }
    }
};
SocketService = __decorate([
    Injectable({
        providedIn: 'root'
    }),
    __param(0, Inject(PLATFORM_ID))
], SocketService);
export { SocketService };
