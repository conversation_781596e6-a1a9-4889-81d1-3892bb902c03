var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { DOCUMENT, isPlatformBrowser, isPlatformServer } from '@angular/common';
import { Inject, Injectable, Optional, PLATFORM_ID } from '@angular/core';
import { REQUEST } from '@nguniversal/express-engine/tokens';
import { environment } from '../../environments/environment';
let AppService = class AppService {
    // configUrl = `${
    //   environment.production
    //     ? '/assets/configs/prod.config.json'
    //     : '/assets/configs/dev.config.json'
    // }`;
    constructor(state, http, platformId, document, request, translate, toastrService) {
        this.state = state;
        this.http = http;
        this.platformId = platformId;
        this.document = document;
        this.request = request;
        this.translate = translate;
        this.toastrService = toastrService;
    }
    get isBrowser() {
        return isPlatformBrowser(this.platformId);
    }
    get isServer() {
        return isPlatformServer(this.platformId);
    }
    async getData() {
        if (this.isServer) {
            const host = this.request.get('host');
            this.baseURL =
                (host.startsWith('localhost') ? 'http://' : 'https://') + host;
            this.environment = await this.fetchData();
            this.state.saveState('environment', this.environment);
        }
        else {
            if (this.state.hasState('environment')) {
                this.environment = this.state.getState('environment');
            }
            else {
                this.baseURL = this.document.location.origin;
                this.environment = await this.fetchData();
                this.state.saveState('environment', this.environment);
            }
        }
    }
    toastSuccess(message) {
        return this.toastrService.success(this.translate.instant(message));
    }
    toastError(err) {
        const getError = (err) => {
            if (!err)
                return 'Something went wrong, please try again';
            if (typeof err === 'string') {
                return err;
            }
            if (err && err.data) {
                return (err.data.data && err.data.data.message) || err.data.message;
            }
            return err.message || 'Something went wrong, please try again';
        };
        return this.toastrService.error(this.translate.instant(getError(err || null)));
    }
    async fetchData() {
        return environment;
        // return await firstValueFrom<any>(
        //   this.http.get(this.baseURL + this.configUrl)
        // );
    }
};
AppService = __decorate([
    Injectable({
        providedIn: 'root'
    }),
    __param(2, Inject(PLATFORM_ID)),
    __param(3, Inject(DOCUMENT)),
    __param(4, Optional()),
    __param(4, Inject(REQUEST))
], AppService);
export { AppService };
