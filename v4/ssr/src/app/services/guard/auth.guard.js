var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
let AuthGuard = class AuthGuard {
    constructor(router, Auth) {
        this.router = router;
        this.Auth = Auth;
    }
    canActivate() {
        // const currentUser = this.stateService.getState(STATE.CURRENT_USER) as IUser;
        // if (!this.Auth.isLoggedin() || !currentUser || !currentUser._id) {
        //   this.router.navigate(['/auth/login']);
        //   return false;
        // }
        // return true;
        return this.Auth.currentUserSubject.pipe(map((userInfo) => {
            if (userInfo) {
                return true;
            }
            return this.router.parseUrl('/auth/login');
        }));
    }
};
AuthGuard = __decorate([
    Injectable({ providedIn: 'root' })
], AuthGuard);
export { AuthGuard };
