var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { APIRequest } from './api-request';
let UserService = class UserService extends APIRequest {
    me() {
        return this.get('/users/me');
    }
    updateMe(data) {
        return this.put('/users', data);
    }
    findOne(id) {
        return this.get(`/users/${id}`);
    }
    inviteFriend(params) {
        return this.post('/newsletter/invite-friend', params);
    }
    deleteAvatar() {
        return this.del('/users/avatar/delete');
    }
    changeEmail(id, data) {
        return this.put(`/users/${id}/change-email`, data);
    }
};
UserService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], UserService);
export { UserService };
