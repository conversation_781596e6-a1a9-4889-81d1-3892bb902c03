var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
let UtilService = class UtilService {
    constructor() {
        this.appLoading = new Subject();
        this.appLoading$ = this.appLoading.asObservable();
        this.eventChange = new Subject();
        this.eventChanged$ = this.eventChange.asObservable();
    }
    setLoading(loading) {
        this.appLoading.next(loading);
    }
    notifyEvent(name, value) {
        this.eventChange.next({
            name,
            value
        });
    }
};
UtilService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], UtilService);
export { UtilService };
