var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { NgModule } from '@angular/core';
import { DefaultImagePipe, EllipsisPipe, AppCurrencyPipe } from './pipes';
import { StarRatingComponent } from './components/star-rating/star-rating.component';
import { NgbRatingModule } from '@ng-bootstrap/ng-bootstrap';
import { TextEllipsisComponent } from './components/uis/ellipsis.component';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';
import { DateRangeComponent } from './components/uis/date-range/date-range.component';
import { MessageMessageModalComponent, SendMessageButtonComponent } from './components/message/send-message-button/send-message-button.component';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SidebarComponent } from './components/uis/sidebar/sidebar.component';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { ViewYoutubeModalComponent } from './components/home/<USER>/popup.component';
let SharedModule = class SharedModule {
};
SharedModule = __decorate([
    NgModule({
        imports: [
            NgbRatingModule,
            CommonModule,
            NgxDaterangepickerMd.forRoot(),
            FormsModule,
            TranslateModule.forChild(),
            RouterModule,
            NgSelectModule
        ],
        declarations: [
            EllipsisPipe,
            StarRatingComponent,
            TextEllipsisComponent,
            DateRangeComponent,
            SendMessageButtonComponent,
            MessageMessageModalComponent,
            DefaultImagePipe,
            SidebarComponent,
            ViewYoutubeModalComponent,
            AppCurrencyPipe
        ],
        exports: [
            EllipsisPipe,
            StarRatingComponent,
            TextEllipsisComponent,
            DateRangeComponent,
            SendMessageButtonComponent,
            DefaultImagePipe,
            SidebarComponent,
            NgSelectModule,
            ViewYoutubeModalComponent,
            AppCurrencyPipe
        ],
        providers: [CurrencyPipe],
        entryComponents: [SendMessageButtonComponent]
    })
], SharedModule);
export { SharedModule };
