var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Injectable } from '@angular/core';
import { of, timer } from 'rxjs';
import { map } from 'rxjs/operators';
let CustomPreLoadingStrategyService = class CustomPreLoadingStrategyService {
    preload(route, fn) {
        const loadRoute = (delay) => delay > 0 ? timer(delay).pipe(map(() => fn())) : fn();
        if (route.data && route.data['preload']) {
            const duration = route.data['loadAfter'] ? route.data['loadAfter'] : 0;
            return loadRoute(duration);
        }
        else {
            return of(null);
        }
    }
};
CustomPreLoadingStrategyService = __decorate([
    Injectable({
        providedIn: 'root'
    })
], CustomPreLoadingStrategyService);
export { CustomPreLoadingStrategyService };
