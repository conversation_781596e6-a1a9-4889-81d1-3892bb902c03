var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { STATE } from 'src/app/services';
let SidebarComponent = class SidebarComponent {
    constructor(authService, systemService, translate, stateService, modalService, cartService, notificationService) {
        this.authService = authService;
        this.systemService = systemService;
        this.translate = translate;
        this.stateService = stateService;
        this.modalService = modalService;
        this.cartService = cartService;
        this.notificationService = notificationService;
        this.tree = [];
        this.showMenu = true;
        this.showMenuChange = new EventEmitter();
        this.languages = [];
        this.flag = '';
        this.cartCount = 0;
        this.showBooking = false;
        this.unreadNotificationCount = 0;
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        if (this.currentUser) {
            this.notificationService.countUnread().then(resp => (this.unreadNotificationCount = resp.data.count || 0));
        }
        this.config = this.stateService.getState(STATE.CONFIG);
        this.languages = this.config.i18n.languages;
        this.userLang = this.config.userLang;
        this.cartService.model.data$.subscribe(resp => {
            const { items } = resp;
            this.cartCount = items.length;
        });
        this.showBooking = this.stateService.showBooking();
    }
    logout() {
        this.authService.removeToken();
        window.location.href = '/';
    }
    toggleMemu() {
        this.showMenuChange.emit(!this.showMenu);
    }
    changeLang(lang) {
        this.userLang = lang.key;
        this.systemService.setUserLang(this.userLang);
        this.translate.use(this.userLang);
    }
};
__decorate([
    Input()
], SidebarComponent.prototype, "showMenu", void 0);
__decorate([
    Output()
], SidebarComponent.prototype, "showMenuChange", void 0);
SidebarComponent = __decorate([
    Component({
        selector: 'app-sidebar',
        templateUrl: './sidebar.component.html'
    })
], SidebarComponent);
export { SidebarComponent };
