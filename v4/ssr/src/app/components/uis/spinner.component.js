var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
import { Component, Input, Inject, ViewEncapsulation } from '@angular/core';
import { NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';
import { DOCUMENT } from '@angular/common';
let SpinnerComponent = class SpinnerComponent {
    constructor(router, document, route) {
        this.router = router;
        this.document = document;
        this.route = route;
        this.isSpinnerVisible = true;
        this.backgroundColor = 'rgba(0, 115, 170, 0.69)';
        this.router.events.subscribe(event => {
            if (event instanceof NavigationStart) {
                this.isSpinnerVisible = true;
            }
            else if (event instanceof NavigationEnd ||
                event instanceof NavigationCancel ||
                event instanceof NavigationError) {
                this.isSpinnerVisible = false;
            }
        }, () => {
            this.isSpinnerVisible = false;
        });
    }
    ngOnDestroy() {
        this.isSpinnerVisible = false;
    }
};
__decorate([
    Input()
], SpinnerComponent.prototype, "backgroundColor", void 0);
SpinnerComponent = __decorate([
    Component({
        selector: 'app-spinner',
        template: `<div class="preloader d-flex justify-content-center align-items-center" *ngIf="isSpinnerVisible">
    <div class="loadingio-spinner-double-ring-zky3qxo1bmo">
      <div class="ldio-54vz96dh9hf">
        <div></div>
        <div></div>
        <div><div></div></div>
        <div><div></div></div>
      </div>
    </div>
  </div>`,
        encapsulation: ViewEncapsulation.None
    }),
    __param(1, Inject(DOCUMENT))
], SpinnerComponent);
export { SpinnerComponent };
