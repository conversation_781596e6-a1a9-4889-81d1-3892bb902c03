var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Output, EventEmitter } from '@angular/core';
import * as moment from 'moment';
let DateRangeComponent = class DateRangeComponent {
    constructor(calendar) {
        this.dateChange = new EventEmitter();
        this.isShow = false;
        this.dateRange = {};
        this.outsideDays = 'visible';
        this.showDates = '';
        this.ranges = {
            Today: [moment(), moment()],
            Yesterday: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        };
        this.invalidDates = [moment().add(2, 'days'), moment().add(3, 'days'), moment().add(5, 'days')];
        this.locale = {
            format: 'MM/DD/YYYY',
            displayFormat: 'MM/DD/YYYY',
            direction: 'ltr',
            weekLabel: 'W',
            separator: ' - ',
            cancelLabel: 'Cancel',
            applyLabel: 'Okay',
            clearLabel: '',
            customRangeLabel: 'Custom range',
            firstDay: 1 // first day is monday
        };
        this.isInvalidDate = (m) => {
            return this.invalidDates.some(d => d.isSame(m, 'day'));
        };
        this.fromDate = calendar.getToday();
        this.alwaysShowCalendars = true;
    }
    toggle() {
        this.isShow = !this.isShow;
    }
    onDateSelection(date) {
        if (date.startDate && date.endDate) {
            return this.dateChange.emit({
                from: moment(new Date(date.startDate)).toISOString(),
                to: moment(new Date(date.endDate)).toISOString()
            });
        }
        this.dateChange.emit(null);
    }
    isHovered(date) {
        return (this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate));
    }
    isInside(date) {
        return date.after(this.fromDate) && date.before(this.toDate);
    }
    isRange(date) {
        return date.equals(this.fromDate) || date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);
    }
};
__decorate([
    Output()
], DateRangeComponent.prototype, "dateChange", void 0);
DateRangeComponent = __decorate([
    Component({
        selector: 'app-date-range',
        templateUrl: './date-range.html'
    })
], DateRangeComponent);
export { DateRangeComponent };
