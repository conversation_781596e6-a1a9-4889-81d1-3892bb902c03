var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
// declare let jQuery: any;
import * as jQuery from 'jquery';
let TextEllipsisComponent = class TextEllipsisComponent {
    constructor(appService) {
        this.appService = appService;
        this.content = '';
        this.showChar = 50;
        this.path = '';
        this.param = '';
    }
    ngOnInit() {
        const showChar = this.showChar;
        if (this.content && this.appService.isBrowser) {
            (function ($) {
                $(document).ready(function () {
                    //   const showChar = showChar; // How many characters are shown by default
                    const ellipsestext = '...';
                    let content = '';
                    $('.more').each(function () {
                        content = $(this).text();
                        if (content.length > showChar) {
                            const c = content.substr(0, showChar);
                            const html = c + '<span class="moreellipses">' + ellipsestext + '&nbsp;</span>' + '</span>&nbsp;&nbsp;' + '</span>';
                            $(this).html(html);
                        }
                        else {
                            const html = content + '<span class="moreellipses">' + '&nbsp;</span>' + '</span>&nbsp;&nbsp;' + '</span>';
                            $(this).html(html);
                        }
                    });
                });
            })(jQuery);
        }
    }
};
__decorate([
    Input()
], TextEllipsisComponent.prototype, "content", void 0);
__decorate([
    Input()
], TextEllipsisComponent.prototype, "showChar", void 0);
__decorate([
    Input()
], TextEllipsisComponent.prototype, "path", void 0);
__decorate([
    Input()
], TextEllipsisComponent.prototype, "param", void 0);
TextEllipsisComponent = __decorate([
    Component({
        selector: 'app-text-ellipsis',
        template: `<div class="card-text" *ngIf="content">
    <span class="more" [innerHTML]="content"></span>
  </div>`
        // <a [routerLink]="[path, param]" class="morelink"><span translate>Read more</span>...</a>
    })
], TextEllipsisComponent);
export { TextEllipsisComponent };
