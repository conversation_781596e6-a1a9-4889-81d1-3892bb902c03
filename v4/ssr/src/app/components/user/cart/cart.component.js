var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let CartComponent = class CartComponent {
    constructor(cartService, stateService, activeModal, router, tutorService, toast) {
        this.cartService = cartService;
        this.stateService = stateService;
        this.activeModal = activeModal;
        this.router = router;
        this.tutorService = tutorService;
        this.toast = toast;
        this.cartItems = [];
        this.config = {};
        this.totalPrice = 0;
        this.loading = false;
        this.cartService.model.data$.subscribe(resp => {
            this.cartItems = resp.items;
            let price = 0;
            if (this.cartItems.length > 0) {
                this.cartItems.forEach(item => (price += item.price));
            }
            this.totalPrice = price;
        });
    }
    ngOnInit() {
        this.config = this.stateService.getState(STATE.CONFIG);
        const tutorInCart = this.cartService.getTutorId();
        this.loading = true;
        this.tutorService.findOne(tutorInCart).then(resp => {
            this.tutor = resp.data;
            this.loading = false;
        });
        console.log('cartItems:', this.cartItems);
    }
    removeItem(item) {
        this.cartService.removeItem(item);
    }
    checkout() {
        if (!this.tutor)
            return this.toast.error('Loading data, please wait...');
        this.activeModal.close({
            checkout: true
        });
        const paymentParams = {
            tutorId: this.cartService.getTutorId(),
            times: this.cartItems.map(item => {
                return {
                    startTime: item.product.startTime,
                    toTime: item.product.toTime,
                    targetId: item.product.targetId,
                    couponCode: item.couponCode
                };
            })
        };
        localStorage.setItem('paymentParams', JSON.stringify(paymentParams));
        return this.router.navigate(['/payments/pay'], {
            queryParams: {
                type: 'booking',
                targetType: 'subject',
                title: `Make your Payment for classes with tutor ${this.tutor.name}`
            },
            state: paymentParams
        });
    }
};
CartComponent = __decorate([
    Component({
        selector: 'app-cart',
        templateUrl: './cart.html',
        styleUrls: ['./card.component.scss']
    })
], CartComponent);
export { CartComponent };
