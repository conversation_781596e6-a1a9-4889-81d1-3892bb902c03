var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
let MessageMessageModalComponent = class MessageMessageModalComponent {
    constructor(service, activeModal, appService) {
        this.service = service;
        this.activeModal = activeModal;
        this.appService = appService;
        this.message = {
            text: ''
        };
        this.submitted = false;
    }
    submit(frm) {
        this.submitted = true;
        if (frm.invalid) {
            return;
        }
        if (!this.message.text) {
            return this.appService.toastError('Please enter message');
        }
        return this.service
            .send({
            conversationId: this.conversation._id,
            type: 'text',
            text: this.message.text
        })
            .then(() => this.activeModal.close({ success: true }));
    }
};
__decorate([
    Input()
], MessageMessageModalComponent.prototype, "conversation", void 0);
MessageMessageModalComponent = __decorate([
    Component({
        templateUrl: './send-message-modal.html'
    })
], MessageMessageModalComponent);
export { MessageMessageModalComponent };
let SendMessageButtonComponent = class SendMessageButtonComponent {
    constructor(appService, modalService, authService, conversationService, router) {
        this.appService = appService;
        this.modalService = modalService;
        this.authService = authService;
        this.conversationService = conversationService;
        this.router = router;
    }
    sendMessage() {
        if (!this.authService.isLoggedin()) {
            return this.appService.toastError('Please login to send message');
        }
        return this.conversationService
            .create(this.recipientId)
            .then((resp) => {
            const modalRef = this.modalService.open(MessageMessageModalComponent, {
                backdrop: 'static',
                keyboard: false
            });
            modalRef.componentInstance.conversation = resp.data;
            modalRef.result.then((result) => {
                if (result && result.success) {
                    this.appService.toastSuccess('Your message has been sent');
                    this.router.navigate(['/users/conversations']);
                }
            });
        })
            .catch(() => this.appService.toastError('You can not send messages to yourself'));
    }
};
__decorate([
    Input()
], SendMessageButtonComponent.prototype, "recipientId", void 0);
SendMessageButtonComponent = __decorate([
    Component({
        selector: 'app-send-message-btn',
        template: `<button
    class="btn btn-default btn-block"
    translate
    (click)="sendMessage()"
  >
    <i class="far fa-envelope color-white me-2"></i>
    <span translate>Send a Message</span>
  </button>`
    })
], SendMessageButtonComponent);
export { SendMessageButtonComponent };
