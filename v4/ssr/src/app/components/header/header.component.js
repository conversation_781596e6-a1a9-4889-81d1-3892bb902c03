var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
import { CartComponent } from '../user/cart/cart.component';
let HeaderComponent = class HeaderComponent {
    constructor(stateService, authService, systemService, translate, cartService, notificationService, modalService) {
        this.stateService = stateService;
        this.authService = authService;
        this.systemService = systemService;
        this.translate = translate;
        this.cartService = cartService;
        this.notificationService = notificationService;
        this.modalService = modalService;
        this.isHome = false;
        this.isOpenedMenu = false;
        this.languages = [];
        this.flag = '/assets/images/flags/en.svg';
        this.isLoaded = false;
        this.showHelloBar = true;
        this.notificationOptions = {
            filter: {
                page: 1,
                take: 1000,
                sort: 'updatedAt',
                sortType: 'desc'
            },
            count: 0,
            unreadNotification: 0,
            notifications: []
        };
        this.isShow = false;
        this.cartCount = 0;
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        this.appConfig = this.stateService.getState(STATE.CONFIG);
        this.showHelloBar = this.appConfig.helloBar.isActive;
        if (Object.keys(this.appConfig).length > 0) {
            this.isLoaded = true;
            this.languages = this.appConfig.i18n.languages;
            this.userLang = this.appConfig.userLang;
            this.languages.map((item) => {
                if (item.key === this.userLang)
                    this.flag = item.flag;
            });
        }
        if (this.currentUser && this.currentUser._id) {
            this.cartService.model.data$.subscribe(resp => {
                const { items } = resp;
                this.cartCount = items.length;
            });
        }
    }
    ngOnInit() {
        if (this.currentUser && this.currentUser._id) {
            this.getNotification();
        }
    }
    logout() {
        this.authService.removeToken();
        // this.router.navigate(['/auth/login']);
        localStorage.removeItem('cartInfo');
        window.location.href = '/';
    }
    changeLang(lang) {
        this.flag = lang.flag;
        this.userLang = lang.key;
        this.systemService.setUserLang(this.userLang);
        this.translate.use(this.userLang);
    }
    // login() {
    // }
    getNotification() {
        this.notificationService.list(this.notificationOptions.filter).then(resp => {
            if (resp && resp.data && resp.data.items && resp.data.items.length) {
                this.notificationOptions.notifications = resp.data.items;
                this.notificationOptions.count = resp.data.count;
                this.notificationOptions.unreadNotification = resp.data.unreadNotification;
            }
        });
    }
    showNotification() {
        this.isShow = !this.isShow;
    }
    onReadNotification(unreadNotification) {
        this.notificationOptions.unreadNotification -= unreadNotification;
        this.isShow = false;
    }
    onRemoveNotification(event) {
        if (event.success) {
            this.getNotification();
        }
    }
    onReadAllNotification() {
        this.notificationOptions.unreadNotification = 0;
        this.isShow = false;
    }
    checkout() {
        const modalRef = this.modalService.open(CartComponent, {
            centered: true,
            size: 'lg'
        });
        modalRef.result.then(res => {
            console.log(res);
        }, () => { return; });
    }
};
HeaderComponent = __decorate([
    Component({
        selector: 'app-header',
        templateUrl: './header.component.html',
        styleUrls: ['./header.component.scss']
    })
], HeaderComponent);
export { HeaderComponent };
