var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component } from '@angular/core';
import { STATE } from 'src/app/services';
let FooterComponent = class FooterComponent {
    constructor(authService, stateService, staticPageService) {
        this.authService = authService;
        this.stateService = stateService;
        this.staticPageService = staticPageService;
        this.staticPages = [];
        this.config = {};
        this.isLoggedin = false;
        this.config = this.stateService.getState(STATE.CONFIG);
    }
    ngOnInit() {
        this.isLoggedin = this.authService.isLoggedin();
        this.staticPageService
            .getPages({ take: 99, isActive: true, sort: 'ordering', sortType: 'asc' })
            .then((resp) => {
            this.staticPages = resp.data.items;
        });
    }
};
FooterComponent = __decorate([
    Component({
        selector: 'app-footer',
        templateUrl: './footer.component.html',
        styleUrls: ['./footer.component.scss']
    })
], FooterComponent);
export { FooterComponent };
