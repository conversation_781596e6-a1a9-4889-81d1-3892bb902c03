var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';
import * as jQuery from 'jquery';
let NotificationComponent = class NotificationComponent {
    constructor(notificationService, appService) {
        this.notificationService = notificationService;
        this.appService = appService;
        this.isShow = false;
        this.doRead = new EventEmitter();
        this.doRemove = new EventEmitter();
        this.closeNotification = new EventEmitter();
        this.doReadAllNotification = new EventEmitter();
        this.removing = false;
        this.activeId = '';
    }
    onClick(btn) {
        const { parentElement } = btn;
        if (!parentElement ||
            !parentElement.className ||
            (parentElement.className &&
                parentElement.className.indexOf('notification-option') < 0)) {
            this.activeId = '';
        }
    }
    read(item, index, url = '', param = '') {
        if (item.unreadNotification > 0) {
            this.notificationService.read(item._id).then((resp) => {
                if (resp.data && resp.data.success) {
                    this.doRead.emit(this.notifications[index].unreadNotification);
                    this.notifications[index].unreadNotification = 0;
                }
            });
        }
        // this.router.navigate(param ? [url, param] : [url]);
    }
    closePopup() {
        this.closeNotification.emit(true);
    }
    removeNotification(item, index) {
        if (window.confirm('Are you sure you want to delete this notification?')) {
            this.removing = true;
            this.notificationService
                .remove(item._id)
                .then((resp) => {
                if (resp.data && resp.data.success) {
                    this.appService.toastSuccess('Removed successfully');
                    this.notifications.splice(index, 1);
                    this.doRemove.emit({ success: true });
                    this.removing = false;
                }
            })
                .catch((err) => this.appService.toastError(err));
        }
    }
    readAll() {
        this.notificationService.readAll().then((resp) => {
            if (resp.data && resp.data.success) {
                for (const n of this.notifications) {
                    if (n.unreadNotification > 0) {
                        n.unreadNotification = 0;
                    }
                }
                // this.notifications = this.notifications.map(n =>  {
                //   if (n.unreadNotification > 0) {
                //     n.unreadNotification = 0;
                //   }
                //   return n
                // })
                this.doReadAllNotification.emit(true);
            }
        });
    }
    selectNotificationOption(item) {
        if (this.activeId && this.activeId === item._id) {
            this.activeId = '';
            return;
        }
        this.activeId = item._id;
    }
    ngAfterViewInit() {
        if (this.appService.isBrowser) {
            (function ($) {
                $(document).ready(function () {
                    $('.notification-option-dropdown').each(function () {
                        // $(this).css('left', $(this).parent().position().left);
                        $(this).css('top', $('.notification-option').height());
                        // $(this).css('min-width', $(this).parent().outerWidth());
                        // var _this = $(this);
                        // $('.notification_scroll').scroll(function () {
                        //   $(_this).css('left', $(_this).parent().position().left);
                        // });
                    });
                });
            })(jQuery);
        }
    }
};
__decorate([
    Input()
], NotificationComponent.prototype, "isShow", void 0);
__decorate([
    Input()
], NotificationComponent.prototype, "notifications", void 0);
__decorate([
    Input()
], NotificationComponent.prototype, "currentUser", void 0);
__decorate([
    Output()
], NotificationComponent.prototype, "doRead", void 0);
__decorate([
    Output()
], NotificationComponent.prototype, "doRemove", void 0);
__decorate([
    Output()
], NotificationComponent.prototype, "closeNotification", void 0);
__decorate([
    Output()
], NotificationComponent.prototype, "doReadAllNotification", void 0);
__decorate([
    HostListener('click', ['$event.target'])
], NotificationComponent.prototype, "onClick", null);
NotificationComponent = __decorate([
    Component({
        selector: 'app-notification',
        templateUrl: './notification.component.html',
        styleUrls: ['./notification.component.scss']
    })
], NotificationComponent);
export { NotificationComponent };
