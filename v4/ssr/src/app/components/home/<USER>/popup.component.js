var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, Input } from '@angular/core';
let ViewYoutubeModalComponent = class ViewYoutubeModalComponent {
    constructor(activeModal, toasty, route, sanitizer) {
        this.activeModal = activeModal;
        this.toasty = toasty;
        this.route = route;
        this.sanitizer = sanitizer;
        this.urlYoutube = null;
    }
    ngOnInit() {
        this.urlYoutube = this.setUrl(this.idYoutube);
    }
    setUrl(idYoutube) {
        return this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.youtube.com/embed/${idYoutube}`);
    }
};
__decorate([
    Input()
], ViewYoutubeModalComponent.prototype, "idYoutube", void 0);
ViewYoutubeModalComponent = __decorate([
    Component({
        selector: 'app-popup-view-video',
        templateUrl: './popup.html'
    })
], ViewYoutubeModalComponent);
export { ViewYoutubeModalComponent };
