var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Component, ViewEncapsulation } from '@angular/core';
import { NavigationEnd } from '@angular/router';
import { filter, map, mergeMap, tap } from 'rxjs/operators';
import { STATE } from 'src/app/services';
import { CartComponent } from 'src/app/components/user/cart/cart.component';
let DashboardLayoutComponent = class DashboardLayoutComponent {
    constructor(router, route, stateService, authService, cartService, modalService, notificationService, systemService, translate) {
        this.router = router;
        this.route = route;
        this.stateService = stateService;
        this.authService = authService;
        this.cartService = cartService;
        this.modalService = modalService;
        this.notificationService = notificationService;
        this.systemService = systemService;
        this.translate = translate;
        this.noShowMenu = false;
        this.showSidebar = true;
        this.mobileScreenWidth = 768;
        this.unreadNotificationCount = 0;
        this.cartCount = 0;
        this.languages = [];
        this.flag = '/assets/images/flags/en.svg';
        this.config = this.stateService.getState(STATE.CONFIG);
        this.languages = this.config.i18n.languages;
        this.userLang = this.config.userLang;
        this.currentUser = this.stateService.getState(STATE.CURRENT_USER);
        if (this.currentUser) {
            this.notificationService.countUnread().then(resp => (this.unreadNotificationCount = resp.data.count || 0));
            this.cartService.model.data$.subscribe(resp => {
                const { items } = resp;
                this.cartCount = items.length;
            });
        }
        this.router.events
            .pipe(filter(event => event instanceof NavigationEnd), map(() => this.route), map((activatedRoute) => {
            while (activatedRoute.firstChild)
                activatedRoute = activatedRoute.firstChild;
            return route;
        }), mergeMap(mergedRoute => {
            return mergedRoute.data;
        }), tap(paramMap => paramMap))
            .subscribe(paramAsMap => {
            this.noShowMenu = paramAsMap['noShowMenu'] ? true : false;
        }
        // Get the params (paramAsMap.params) and use them to highlight or everything that meet your need
        );
    }
    ngOnInit() {
        this.onReadNotificationSubscription = this.notificationService.readNotification$.subscribe(value => {
            if (value > 0 && this.unreadNotificationCount >= value) {
                this.unreadNotificationCount -= value;
            }
        });
        if (window.innerWidth <= this.mobileScreenWidth)
            this.showSidebar = false;
    }
    logout() {
        this.authService.removeToken();
        window.location.href = '/';
    }
    showMenuChange(data) {
        this.showSidebar = data;
    }
    toggleMemu() {
        this.showSidebar = !this.showSidebar;
    }
    checkout() {
        const modalRef = this.modalService.open(CartComponent, {
            centered: true,
            size: 'lg'
        });
        modalRef.result.then(res => {
            console.log(res);
        }, () => { return; });
    }
    changeLang(lang) {
        this.flag = lang.flag;
        this.userLang = lang.key;
        this.systemService.setUserLang(this.userLang);
        this.translate.use(this.userLang);
    }
    ngOnDestroy() {
        this.onReadNotificationSubscription.unsubscribe();
    }
};
DashboardLayoutComponent = __decorate([
    Component({
        templateUrl: './dashboard.component.html',
        styleUrls: ['./dashboard.scss'],
        encapsulation: ViewEncapsulation.None
    })
], DashboardLayoutComponent);
export { DashboardLayoutComponent };
