{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"scss-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/sitemap.xml", "src/robots.txt", "src/manifest.webmanifest", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-sharebuttons/themes/circles.scss", "node_modules/@flaticon/flaticon-uicons/css/all/all.css", "node_modules/slick-carousel/slick/slick.scss", "node_modules/slick-carousel/slick/slick-theme.scss", "src/assets/scss/style.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "node_modules/jquery/dist/jquery.min.js", "node_modules/slick-carousel/slick/slick.min.js", "node_modules/flexslider/jquery.flexslider-min.js"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json", "allowedCommonJsDependencies": ["lodash", "moment", "j<PERSON>y"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "scss-angular:build:production"}, "development": {"browserTarget": "scss-angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "scss-angular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/sitemap.xml", "src/robots.txt", "src/manifest.webmanifest"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "src/assets/scss/style.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "karmaConfig": "karma.conf.js"}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/server", "main": "src/main.server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media"}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@nguniversal/builders:ssr-dev-server", "configurations": {"development": {"browserTarget": "scss-angular:build:development", "serverTarget": "scss-angular:server:development"}, "production": {"browserTarget": "scss-angular:build:production", "serverTarget": "scss-angular:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@nguniversal/builders:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "scss-angular:build:production", "serverTarget": "scss-angular:server:production"}, "development": {"browserTarget": "scss-angular:build:development", "serverTarget": "scss-angular:server:development"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": false}}