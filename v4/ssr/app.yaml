runtime: nodejs22

# Service configuration
service: ssr

# Instance configuration
instance_class: F2
automatic_scaling:
  min_instances: 1
  max_instances: 8
  target_cpu_utilization: 0.6

# Environment variables
env_variables:
  NODE_ENV: production
  PORT: 8080
  # API service connection
  API_BASE_URL: https://api-dot-vocabulous-466003.et.r.appspot.com/v1
  SOCKET_URL: https://api-dot-vocabulous-466003.et.r.appspot.com
  # Application URLs
  SSR_URL: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  ADMIN_URL: https://admin-dot-vocabulous-466003.et.r.appspot.com
  # App configuration
  APP_NAME: LIVELEARN
  VERSION: regular

# Health check configuration
readiness_check:
  path: '/health'
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2

liveness_check:
  path: '/health'
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 4
  success_threshold: 2

# Network configuration
network:
  forwarded_ports:
    - 8080

# Let Express server handle all requests including static files
handlers:
  - url: /.*
    script: auto
    secure: always
