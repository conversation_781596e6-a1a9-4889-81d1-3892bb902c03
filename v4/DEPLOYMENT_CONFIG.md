# Deployment Configuration Summary

This document outlines the environment variable configuration for connecting the SSR, API, and Admin services in Google App Engine.

## Service URLs

- **SSR**: https://ssr-dot-vocabulous-466003.et.r.appspot.com
- **API**: https://api-dot-vocabulous-466003.et.r.appspot.com  
- **Admin**: https://admin-dot-vocabulous-466003.et.r.appspot.com

## Configuration Files Updated

### 1. SSR Service (`v4/ssr/app.yaml`)
```yaml
env_variables:
  NODE_ENV: production
  PORT: 8080
  # API service connection
  API_BASE_URL: https://api-dot-vocabulous-466003.et.r.appspot.com/v1
  SOCKET_URL: https://api-dot-vocabulous-466003.et.r.appspot.com
  # Application URLs
  SSR_URL: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  ADMIN_URL: https://admin-dot-vocabulous-466003.et.r.appspot.com
  # App configuration
  APP_NAME: LIVELEARN
  VERSION: regular
```

### 2. API Service (`v4/api/app.yaml`)
```yaml
env_variables:
  NODE_ENV: production
  APP_NAME: LIVELEARN
  ALLOW_CORS: true
  PORT: 8080
  VERSION: regular
  
  # Service URLs
  baseUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/
  userWebUrl: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  adminWebUrl: https://admin-dot-vocabulous-466003.et.r.appspot.com
  
  # Site configuration
  SITE_NAME: LIVELEARN
  logoUrl: https://api-dot-vocabulous-466003.et.r.appspot.com/assets/logo.png
  
  # CORS and security
  host: vocabulous-466003.et.r.appspot.com
  
  # Session configuration (replace with your actual secret)
  SESSION_SECRET: your-production-session-secret-change-this
  
  # File size limits (in MB)
  MAX_PHOTO_SIZE: 10
  MAX_VIDEO_SIZE: 200
  MAX_FILE_SIZE: 100
```

### 3. Admin Service (`v4/admin/app.yaml`)
```yaml
env_variables:
  # Application environment
  ENVIRONMENT: production
  
  # Service URLs
  API_URL: https://api-dot-vocabulous-466003.et.r.appspot.com
  SSR_URL: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  ADMIN_URL: https://admin-dot-vocabulous-466003.et.r.appspot.com
  
  # App configuration
  APP_NAME: LIVELEARN
  VERSION: 4.0.0
```

### 4. Admin Production Config (`v4/admin/src/assets/configs/prod.config.json`)
```json
{
  "production": true,
  "version": "4.0",
  "build": 1,
  "maximumFileSize": 15,
  "apiBaseUrl": "https://api-dot-vocabulous-466003.et.r.appspot.com/v1",
  "platform": "admin",
  "showBuild": false,
  "stripeKey": "pk_test_xxxx",
  "url": "https://admin-dot-vocabulous-466003.et.r.appspot.com",
  "socketUrl": "https://api-dot-vocabulous-466003.et.r.appspot.com",
  "zoomSDK": "LYbtxxx",
  "zoomSiteUrl": "https://lesson.vocabulous-466003.et.r.appspot.com"
}
```

### 5. SSR Production Environment (`v4/ssr/src/environments/environment.prod.ts`)
```typescript
export const environment = {
  production: true,
  version: '4.0.0',
  build: 3,
  showBuild: false,
  maximumFileSize: 1024,
  apiBaseUrl: 'https://api-dot-vocabulous-466003.et.r.appspot.com/v1',
  stripeKey: 'pk_test_xxxx', // Replace with your actual Stripe key
  url: 'https://ssr-dot-vocabulous-466003.et.r.appspot.com',
  socketUrl: 'https://api-dot-vocabulous-466003.et.r.appspot.com',
  zoomSDK: 'LYbtxxx', // Replace with your actual Zoom SDK key
  zoomSiteUrl: 'https://lesson.vocabulous-466003.et.r.appspot.com'
};
```

## Important Notes

### Security Configuration Required
1. **SESSION_SECRET**: Replace `your-production-session-secret-change-this` with a strong, unique secret
2. **Stripe Key**: Replace `pk_test_xxxx` with your actual Stripe publishable key
3. **Zoom SDK**: Replace `LYbtxxx` with your actual Zoom SDK key

### Database & External Services
The API service will need additional environment variables for:
- MongoDB connection (`MONGO_URI`)
- Redis connection (`REDIS_HOST`, `REDIS_PORT`, etc.)
- Email service configuration (SendGrid, etc.)
- Payment processing (Stripe secret key)
- Social authentication (Google, Facebook)

### CORS Configuration
The API service is configured to allow CORS from the SSR and Admin domains. The `ALLOW_CORS: true` setting enables cross-origin requests.

### Health Checks
All services are configured with health check endpoints at `/health` for Google App Engine monitoring.

## Deployment Order
1. Deploy API service first (other services depend on it)
2. Deploy SSR service
3. Deploy Admin service

## Testing Connectivity
After deployment, verify:
1. SSR can connect to API at `/v1/system/configs/public`
2. Admin can connect to API at `/v1/auth/login`
3. Socket connections work between SSR and API
4. Static file serving works for all services
