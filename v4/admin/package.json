{"name": "back-office-pin-learn", "version": "4.0.0", "license": "private", "scripts": {"ng": "ng", "start": "ng serve", "build": "NODE_OPTIONS='--openssl-legacy-provider' ng build --configuration production", "deploy": "NODE_OPTIONS='--openssl-legacy-provider' npm run build && gcloud app deploy", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^10.0.11", "@angular/common": "^10.0.11", "@angular/compiler": "^10.0.11", "@angular/core": "^10.0.11", "@angular/forms": "^10.0.11", "@angular/http": "^7.2.16", "@angular/localize": "^10.0.11", "@angular/platform-browser": "^10.0.11", "@angular/platform-browser-dynamic": "^10.0.11", "@angular/router": "^10.0.11", "@flaticon/flaticon-uicons": "^2.3.0", "@fullcalendar/angular": "^5.3.0", "@fullcalendar/daygrid": "^5.3.0", "@fullcalendar/interaction": "^5.3.0", "@fullcalendar/list": "^5.3.0", "@fullcalendar/timegrid": "^5.3.0", "@ng-bootstrap/ng-bootstrap": "^7.0.0", "@ng-select/ng-select": "^5.0.1", "admin-lte": "^3.2.0", "angular-froala-wysiwyg": "^3.2.1-1", "bootstrap": "^4.1.1", "bootstrap-icons": "^1.9.1", "chart.js": "^2.7.3", "chartist": "^0.11.0", "core-js": "^3.6.5", "file-saver": "^2.0.5", "fullcalendar": "^5.3.0", "jasmine": "^3.1.0", "javascript-obfuscator": "^4.0.0", "jquery": "^3.5.1", "lodash": "^4.17.10", "moment-range": "^4.0.2", "moment-timezone": "^0.5.33", "ng2-file-upload": "^4.0.0", "ngx-doc-viewer": "^2.0.4", "ngx-extended-pdf-viewer": "^7.0.0-beta.6", "ngx-image-cropper": "^3.2.1", "ngx-perfect-scrollbar": "^10.0.1", "ngx-quill": "^13.0.1", "ngx-restangular": "^6.0.0", "ngx-toastr": "^13.0.0", "pace-js": "^1.0.2", "popper.js": "^1.12.5", "primeng": "^10.0.3", "quill": "^1.3.7", "rxjs": "^6.2.0", "rxjs-compat": "^6.0.0-rc.0", "sortablejs": "^1.7.0", "zone.js": "^0.11.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1000.6", "@angular-devkit/schematics": "^10.0.6", "@angular/cli": "^10.0.6", "@angular/compiler-cli": "^10.0.11", "@angular/language-service": "^10.0.11", "@types/chartist": "^0.11.0", "@types/jasmine": "~3.5.12", "@types/jasminewd2": "~2.0.2", "@types/jquery": "^3.5.4", "@types/node": "~14.6.0", "codelyzer": "~6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.2", "karma": "~5.1.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "~2.0.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "~4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "protractor": "~7.0.0", "sass": "^1.77.0", "ts-node": "~8.10.2", "tslint": "~6.1.3", "typescript": "^3.9.7"}, "volta": {"node": "22.16.0"}}