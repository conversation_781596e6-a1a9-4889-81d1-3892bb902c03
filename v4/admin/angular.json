{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app-admin": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "allowedCommonJsDependencies": ["lodash", "rxjs", "rxjs-compat", "ng2-file-upload"], "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["src/styles.css", "src/assets/scss/style.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/pace-js/pace.min.js", "node_modules/bootstrap/dist/js/bootstrap.bundle.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "allowedCommonJsDependencies": ["lodash", "rxjs", "rxjs-compat"], "options": {"browserTarget": "app-admin:build", "port": 1337}, "configurations": {"production": {"browserTarget": "app-admin:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app-admin:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/pace-js/pace.min.js", "node_modules/chart.js/dist/Chart.js"], "styles": ["src/styles.css", "src/assets/scss/style.scss"], "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "app-admin-e2e": {"root": "", "sourceRoot": "", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "app-admin:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "app-admin", "schematics": {"@schematics/angular:component": {"prefix": "app", "styleext": "css"}, "@schematics/angular:directive": {"prefix": "app"}}, "cli": {"analytics": false}}