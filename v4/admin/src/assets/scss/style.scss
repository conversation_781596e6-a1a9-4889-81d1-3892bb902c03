// @import '../../../node_modules/bootstrap/scss/bootstrap';
// @import 'app.scss';
// @import 'pages.scss';
// @import 'sidebar.scss';
@import url('https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.3.0/css/flag-icon.min.css');
@import url('https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700');
// @import 'widgets';
// @import 'grid.scss';
// @import 'spinners.scss';
// @import 'animate.css';
// @import 'theme-settings.scss';
// @import './theme/adminlte.scss';
@import '~bootstrap-icons/font/bootstrap-icons.css';
@import '../../../node_modules/ngx-toastr/toastr.css';
@import '../../../node_modules/@ng-select/ng-select/themes/default.theme.css';
@import '../../../node_modules/fullcalendar/main.min.css';

@import '../../../node_modules/quill/dist/quill.core.css';
@import '../../../node_modules/quill/dist/quill.bubble.css';
@import '../../../node_modules/quill/dist/quill.snow.css';
// @import '~admin-lte/dist/css/adminlte.min.css';
// @import '~admin-lte/plugins/fontawesome-free/css/all.min.css';
// @import '~@flaticon/flaticon-uicons/css/Regular/all';
// @import '~admin-lte/plugins/icheck-bootstrap/icheck-bootstrap.min.css';
// @import 'custom.scss';
// @import 'variable.scss';
// @import './responsive.scss';

// colors

// @import 'colors/default-dark';
body {
  width: 100vw;
  height: 100vh;
}

// ::-webkit-scrollbar {
//   width: 0px;
//   background: transparent;
// }

.content-wrapper {
  .content-header {
    padding: 20px;
  }
  > .content {
    padding: 0 20px 20px 20px;
  }
}

.fi {
  display: inline-block;
  vertical-align: middle;
}

// Sidebar menu animation
// Needed for open/close menu animation for menus with child nodes
.nav .nav-item:not(.menu-open) > .ng-trigger-openClose,
.nav .nav-item > .ng-trigger-openClose.ng-animating {
  overflow: hidden;
  display: block;
}

// Override original AdminLTE transform
.nav-sidebar .menu-is-opening > .nav-link i.right,
.nav-sidebar .menu-is-opening > .nav-link svg.right,
.nav-sidebar .menu-open > .nav-link i.right,
.nav-sidebar .menu-open > .nav-link svg.right {
  transform: none;
}

.pointer {
  cursor: pointer;
}

.media-gallery-item {
  width: 100px;
  font-size: 100px;
}

.upload-zone {
  padding: 10px 0;

  .my-drop-zone {
    position: relative;
    border: dotted 3px lightgray;
    height: 100px;
    overflow: hidden;

    p {
      position: absolute;
      left: 50%;
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
    }

    label {
      height: 200px;

      input {
        height: 200px;
      }
    }
  }

  .nv-file-over {
    border: dotted 3px red;
  }

  /* Default class applied to drop zones on over */
  .another-file-over-class {
    border: dotted 3px green;
  }
}

// trick to hide froala license
a[href="https://froala.com/wysiwyg-editor"],
a[href="https://www.froala.com/wysiwyg-editor?k=u"]
{
  display: none !important;
  position: absolute;
  top: -99999999px;
}

ul {
  &.sortable {
    list-style: none;
    padding-left: 0;
  }
}

.sortable {
  li {
    cursor: move;
  }

  tr {
    cursor: move;
  }
}

.form-group {
  label {
    font-weight: 600;
  }
}

.modal-media {
  .modal-dialog {
    width: 90% !important;
    max-width: calc(90%);

    .nav-item {
      .nav-link {
        &.active {
          font-weight: bold;
        }
      }
    }

    .media-thumb {
      .media-gallery-item {
        width: 100%;
      }
    }
  }
}
