runtime: python39

# Service configuration
service: admin

# Instance configuration
instance_class: F1
automatic_scaling:
  min_instances: 1
  max_instances: 5

# Static file handlers for Angular admin app
handlers:
  # Handle Angular assets
  - url: /assets
    static_dir: dist/assets
    secure: always
    expiration: 1d

  # Handle JavaScript files with proper MIME type
  - url: /(.*\.js)$
    static_files: dist/\1
    upload: dist/.*\.js$
    secure: always
    expiration: 1d
    mime_type: application/javascript

  # Handle CSS files
  - url: /(.*\.css)$
    static_files: dist/\1
    upload: dist/.*\.css$
    secure: always
    expiration: 1d
    mime_type: text/css

  # Handle other static files
  - url: /(.*\.(ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))$
    static_files: dist/\1
    upload: dist/.*\.(ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$
    secure: always
    expiration: 7d

  # Handle source maps (optional, for debugging)
  - url: /(.*\.map)$
    static_files: dist/\1
    upload: dist/.*\.map$
    secure: always
    expiration: 1h

  # Handle all other routes with index.html (Angular routing)
  - url: /.*
    static_files: dist/index.html
    upload: dist/index\.html
    secure: always

# Environment variables for build configuration
env_variables:
  # Application environment
  ENVIRONMENT: production

  # Service URLs
  API_URL: https://api-dot-vocabulous-466003.et.r.appspot.com
  SSR_URL: https://ssr-dot-vocabulous-466003.et.r.appspot.com
  ADMIN_URL: https://admin-dot-vocabulous-466003.et.r.appspot.com

  # App configuration
  APP_NAME: LIVELEARN
  VERSION: 4.0.0
