#!/bin/bash

echo "🚀 Deploying SSR with Fixes"
echo "============================"

cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

echo "📋 Current Configuration:"
echo "- Start script: $(grep '"start"' package.json)"
echo "- Node version: $(grep '"node"' package.json)"
echo ""

echo "🔧 Rebuilding SSR..."
rm -rf dist/
npm run build

echo ""
echo "📁 Checking build output:"
ls -la dist/
echo ""

echo "🚀 Deploying to Google App Engine..."
echo "Run this command manually:"
echo "cd /Users/<USER>/Downloads/Pinlearn/v4/ssr && gcloud app deploy"
echo ""

echo "🔍 After deployment, test these:"
echo "1. Health check: curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/health"
echo "2. Main page: curl https://ssr-dot-vocabulous-466003.et.r.appspot.com/"
echo "3. Browser console: Open dev tools and check for errors"
echo ""

echo "📋 If still blank, check logs:"
echo "gcloud app logs tail -s ssr --limit=20"
