#!/bin/bash

echo "🔍 API Service Debugging Commands"
echo "=================================="
echo ""

echo "1. Check API service logs:"
echo "gcloud app logs tail -s api --limit=50"
echo ""

echo "2. Check API service status:"
echo "gcloud app services list"
echo ""

echo "3. Check API service versions:"
echo "gcloud app versions list --service=api"
echo ""

echo "4. Check API service instances:"
echo "gcloud app instances list --service=api"
echo ""

echo "5. Test API health endpoint:"
echo "curl -v https://api-dot-vocabulous-466003.et.r.appspot.com/health"
echo ""

echo "6. Test API base endpoint:"
echo "curl -v https://api-dot-vocabulous-466003.et.r.appspot.com/"
echo ""

echo "7. Check deployment status:"
echo "gcloud app operations list --limit=5"
echo ""

echo "Run these commands one by one to diagnose the issue."
