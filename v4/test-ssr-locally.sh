#!/bin/bash

echo "🧪 Testing SSR Build Locally"
echo "============================="
echo ""

cd /Users/<USER>/Downloads/Pinlearn/v4/ssr

echo "🏗️ Building SSR..."
npm run build

echo ""
echo "🔍 Checking if main.js was created:"
if [ -f "dist/server/main.js" ]; then
    echo "✅ dist/server/main.js exists"
    
    echo ""
    echo "🚀 Starting SSR server locally..."
    echo "Visit http://localhost:8080 to test"
    echo "Press Ctrl+C to stop"
    echo ""
    
    # Set environment variables for local testing
    export NODE_ENV=production
    export API_BASE_URL=https://api-dot-vocabulous-466003.et.r.appspot.com/v1
    export SOCKET_URL=https://api-dot-vocabulous-466003.et.r.appspot.com
    
    # Start the server
    node dist/server/main.js
else
    echo "❌ dist/server/main.js is missing!"
    echo ""
    echo "Available files in dist/:"
    find dist/ -name "*.js" | head -10
    echo ""
    echo "This indicates the SSR build failed."
    echo "Check the build output above for errors."
fi
